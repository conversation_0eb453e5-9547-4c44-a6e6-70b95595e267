[package]
name = "rouv_scrape"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[workspace]
members = ["prisma-cli"]
resolver = "2"

[dependencies]
dotenvy = "0.15.7"
reqwest = { version = "0.12", features = ["blocking", "json", "socks", "cookies"] }
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
headless_chrome = {git = "https://github.com/atroche/rust-headless-chrome", features = ["fetch"]}
regex = "1.10.2"
m3u8-rs = "6.0.0"
image = "0.25.5"
chrono = "0.4.31"
prisma-client-rust = { git = "https://github.com/Bren<PERSON><PERSON>/prisma-client-rust", tag = "0.6.11", default-features = false, features = [
    "postgresql",
] }
scraper = "0.22.0"
url = "2.5.0"
aws-sdk-s3 = { version = "1", features = ["behavior-version-latest"] }
aws-credential-types = "1.0.3"
bytes = "1.6.1"
reqwest-middleware = "0.4.0"
reqwest-retry = "0.7.0"
async-trait = "0.1.81"
http = "1.2.0"
clap = { version = "4.5.20", features = ["derive"] }
elasticsearch = "8.17.0-alpha.1"
rustls = { version = "0.20.6", features = ["dangerous_configuration"] }
hyper-rustls = "0.23.0"
aws-smithy-runtime = { version = "1", default-features = false, features = [
    "client",
    "connector-hyper-0-14-x",
] }
character_converter = "2.1.5"
meilisearch-sdk = "0.27.1"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }