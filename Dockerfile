# Start with a Rust base image
FROM rust:1.82 as builder

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy the actual source code
COPY . .

# Rebuild the application
RUN cargo prisma generate
RUN cargo build --release

# Our final base
FROM debian:bookworm-slim

WORKDIR /rou

# Install any runtime dependencies (if needed)
RUN apt-get update && apt-get install -y \
  chromium libssl3 \
  && rm -rf /var/lib/apt/lists/*

# Copy the build artifact from the builder stage
COPY --from=builder /usr/src/app/target/release/rouv_scrape .
RUN touch /rou/.env

# Set the startup command to run your binary
CMD ["/rou/rouv_scrape"]