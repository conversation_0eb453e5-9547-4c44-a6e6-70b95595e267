pipeline {
    agent any
    options {
        timeout(time: 1, unit: 'HOURS')
    }
    stages {
        stage('Build') {
            environment { 
                IMAGE_TAG = sh(script: 'git describe --always', returnStdout: true).trim()
            }
            steps {
                withCredentials([file(credentialsId: 'npmrc', variable: 'npmrc_content')]) {
                    writeFile file: '.npmrc', text: readFile(npmrc_content)
                }
                sh "echo Building image with tag: $IMAGE_TAG"
                sh label: "docker build", script: """
                    docker build . -t registry.imgdot.dev/rouv_scrape:$IMAGE_TAG --progress=plain
                """
                withCredentials([usernamePassword(credentialsId: 'docker-imgdot', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
                    sh 'echo "Logging in to Docker registry"'
                    sh 'docker login registry.imgdot.dev -u="${DOCKER_USERNAME}" -p="${DOCKER_PASSWORD}"'
                    sh "echo Pushing image: registry.imgdot.dev/rouv_scrape:$IMAGE_TAG"
                    sh "docker push registry.imgdot.dev/rouv_scrape:$IMAGE_TAG"
                }
            }
        }
    }
}