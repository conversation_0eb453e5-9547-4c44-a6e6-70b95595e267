## How to run

```
cargo rouv cavv https://cableav.tv/KlpAFNjvoMc/
```

## generate prisma client

```
cargo prisma generate
```


## go to db and update: ref and tags

```
UPDATE "Video"
SET tags = array_remove(tags, '222') || '{"chenYY01"}'
WHERE tags @> ARRAY['222'];
```

```
pd run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=password postgres:17
```

## start flaresolverr in podman
```
pd run -d \
  --name=flaresolverr \
  -p 8191:8191 \
  -e LOG_LEVEL=info \
  --restart unless-stopped \
  ghcr.io/flaresolverr/flaresolverr:latest
```

## start the redis cache

```
pd run --name redis -p 16379:6379 -d docker.io/redis:7.2
```

## CUID

https://www.getuniqueid.com/cuid


## upload local video

1. create video record in db with cuid
2. use the cuid as the folder name, put the video and cover.jpg into it
3. run something like go run . locv /Users/<USER>/prj/rouone/data/tmp/cldk3qwiz000008mv69rycdom

## deploy

```
helm upgrade -i rouv-scrape -f rouv_scrape_secret_prod.yaml ./deployment -n rouv
```

## refs

```
https://playav.tv/


https://u3c3.com

https://javday.tv/
```