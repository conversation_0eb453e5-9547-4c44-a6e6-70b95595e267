apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ template "app.fullname" . }}-delete
  labels:
    app: {{ template "app.fullname" . }}
spec:
  schedule: "36 * * * *"
  # schedule: "33 * * * *"
  jobTemplate:
    spec:
      template:
        spec:
    {{- if .Values.nodeSelector }}
          nodeSelector: 
    {{ toYaml .Values.nodeSelector | indent 8 }}
    {{- end }}
    {{- if .Values.imagePullSecrets }}
          imagePullSecrets:
          - name: "{{ .Release.Name }}-docker-registry-secret"
    {{- end }}                    
          containers:
            - name: rouv-scrape
              image: "{{ .Values.image.repo }}:{{ .Values.image.tag }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              args:
                - /bin/sh
                - -c
                - /rou/rouv_scrape delete
              # args:
              #   - tail
              #   - -f 
              #   - /dev/null
              env:
                - name: DATABASE_URL
                  valueFrom:
                    secretKeyRef:
                      name: {{ template "app.fullname" . }}-secrets
                      key: dbUrl
                - name: MEILI_URL
                  value: {{ .Values.meiliUrl | quote }}
                - name: MEILI_API_KEY
                  value: {{ .Values.meiliApiKey | quote }}
                - name: MEILI_INDEX
                  value: {{ .Values.meiliIndex | quote }}
                - name: S3_ENDPOINT
                  value: {{ .Values.s3Endpoint | quote }}
                - name: S3_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: {{ template "app.fullname" . }}-secrets
                      key: s3AccessKey
                - name: S3_SECRET_KEY
                  valueFrom:
                    secretKeyRef:
                      name: {{ template "app.fullname" . }}-secrets
                      key: s3SecretKey
                - name: S3_BUCKET
                  value: {{ .Values.s3Bucket | quote }}
                - name: S3_REGION
                  value: US
                - name: S3_SSL
                  value: {{ .Values.s3SSL | quote }}
                - name: USER_AGENT
                  value: {{ .Values.userAgent | quote }}
                - name: HTTP_CONNECT_TIMEOUT
                  value: {{ .Values.httpConnectTimeout | default "20" | quote }}
                - name: HTTP_TIMEOUT_SHORT
                  value: {{ .Values.httpTimeoutShort | default "100" | quote }}
                - name: HTTP_TIMEOUT_LONG
                  value: {{ .Values.httpTimeoutLong | default "1800" | quote }}
              resources:
                # limits:
                #   memory: 450Mi
                requests:
                  cpu: 100m
                  memory: 100Mi
          restartPolicy: OnFailure
      backoffLimit: 2
  concurrencyPolicy: Forbid
