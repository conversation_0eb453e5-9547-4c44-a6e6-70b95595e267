apiVersion: v1
kind: Secret
metadata:
  name: {{ template "app.fullname" . }}-secrets
  labels:
    app: {{ template "app.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: kubernetes.io/Opaque
data:
  dbUrl: {{ .Values.dbUrl | b64enc | quote }}
  esPassword: {{ .Values.esPassword | b64enc | quote }}
  s3AccessKey: {{ .Values.s3AccessKey | b64enc | quote }}
  s3SecretKey: {{ .Values.s3SecretKey | b64enc | quote }}
  gTranslateAPIKey: {{ .Values.gTranslateAPIKey | b64enc | quote }}
  meiliApiKey: {{ .Values.meiliApiKey | b64enc | quote }}