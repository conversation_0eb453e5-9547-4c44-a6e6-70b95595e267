---
# How many number of pod you desire to spin up.
replicaCount: 1

# A node selector label.
# nodeSelector: { "kubernetes.io/hostname": "node1" }

# Node and inter-pod affinity configuration
affinity: {}

# Toleration for K8s taints
tolerations: []

# Docker image repository, tag and a policy for Kubernetes to pull it.
image:
  repo: registry.imgdot.dev/rouv_scrape
  tag: "1104619"
  pullPolicy: IfNotPresent

imagePullSecrets:
  registry: registry.imgdot.dev
  username: axu
  password: ""

# Kubernetes API version to use for imgproxy Deployment
apiVersion: apps/v1

# Set kubernetes specific resource limits
resources:
  {}
  # limits:
  #   cpu: 700m
  #   memory: 700Mi
  # requests:
  #   cpu: 700m
  #   memory: 700Mi

# Kubernetes service type
serviceType: "ClusterIP"

# Timeuouts and counters options for Liveness & Readiness probes for imgproxy
# Kubernets Deployment
readinessProbe:
  initialDelaySeconds: 10
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 5
livenessProbe:
  initialDelaySeconds: 50
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 5

# Configuration parameters for Ingress resource
ingress:
  acme: false
  annotations: {}
  enabled: false
  health:
    # Comma separated string of CIDR addresses that are allowed
    # to access `/health` url of imgproxy.
    whitelist: ""
  host: "example.com"
  tls:
    enabled: false
    crt: ""
    key: ""
    secretName: ""
  #   nginx.ingress.kubernetes.io/proxy-body-size: "32m"

enablePrometheus: false
prometheusNamespace: ""
serviceMonitor:
  enabled: false
  honorLabels: true
  interval: 0
  namespace: ""
  selector:
    release: prometheus-operator
  targetLabels:
    - app
    - release

podDisruptionBudget:
  enabled: true
  minAvailable: 0
  maxUnavailable: 0

# Pod annotations
pod:
  annotations: {}

# Deployment annotations
deployment:
  annotations: {}

dbUrl: xxx
s3Bucket: rouv
s3AccessKey: xxx
s3SecretKey: xxx
s3Endpoint: https://minio.s3.svc.cluster.local
s3SSL: true
esUrl: http://elasticsearch.db.svc.cluster.local:9200
# esUser: sadmin
# esPassword: x
esUser: x
esPassword: x
esIndex: index_rouv

userAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
meiliUrl: http://meilisearch.db.svc.cluster.local:7700
meiliIndex: rouv_video

# HTTP timeout settings (can be set via .env or overridden here)
httpConnectTimeout: 20  # in seconds
httpTimeoutShort: 100   # in seconds
httpTimeoutLong: 1800   # in seconds