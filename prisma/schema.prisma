// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  // Corresponds to the cargo alias created earlier
  provider = "cargo prisma"
  // The location to generate the client. Is relative to the position of the schema
  output   = "../src/prisma.rs"
}

model Video {
  id            String         @id @default(cuid())
  vid           String? // 番号
  name          String
  description   String?
  ref           String         @unique
  tags          String[]
  sources       Source[]
  published     Boolean        @default(false)
  publisher     String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime?
  viewCount     Int
  likeCount     Int            @default(0)
  dislikeCount  Int            @default(0)
  duration      Float          @default(0)
  archived      Boolean        @default(false)
  archivedAt    DateTime?
  userHistories UserHistory[]
  userFavorites UserFavorite[]

  @@unique([vid, name])
  @@index([published, createdAt, viewCount])
  @@index([vid, published, createdAt, viewCount])
  @@index([tags, published, createdAt, viewCount])
  @@index([publisher, published, createdAt, viewCount])
}

model Source {
  id         String @id @default(cuid())
  video      Video  @relation(fields: [videoId], references: [id])
  videoId    String
  resolution Int
  folder     String @unique
}

model Tag {
  id     String @id
  count  Int    @default(0)
  parent String
  level  Int

  @@index([parent])
  @@index([level])
}

model Account {
  id                 String  @id @default(cuid())
  userId             String
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String?
  access_token       String?
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?
  session_state      String?
  oauth_token_secret String?
  oauth_token        String?
  refresh_expires_in Int?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String         @id @default(cuid())
  name          String?
  email         String?        @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  userHistories UserHistory[]
  userFavorites UserFavorite[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserHistory {
  id     String @id @default(cuid())
  user   User   @relation(fields: [userId], references: [id])
  userId String

  video   Video  @relation(fields: [videoId], references: [id])
  videoId String

  viewedAt DateTime @default(now())

  @@unique([userId, videoId])
  @@index([userId, viewedAt])
}

model UserFavorite {
  id     String @id @default(cuid())
  user   User   @relation(fields: [userId], references: [id])
  userId String

  video   Video  @relation(fields: [videoId], references: [id])
  videoId String

  createdAt DateTime @default(now())

  @@unique([userId, videoId])
  @@index([userId, createdAt])
  @@index([videoId])
}

enum JobStatus {
  CREATE
  IN_PROGRESS
  COMPLETE
  ERROR
}

model ScrapeJob {
  id          String    @id @default(cuid())
  url         String    @unique
  defaultName String?
  defaultTags String[]
  status      JobStatus @default(CREATE)
  message     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime?
}

model Setting {
  id          String   @id @default(cuid())
  annoucement String?
  busyMode    Boolean  @default(false)
  createdAt   DateTime @default(now())
  active      Boolean  @default(false)

  @@index([active, createdAt])
}
