use crate::actions::cover;
use crate::util::browser;
use crate::util::ffmpeg;
use crate::util::video_util;
use crate::Context;
use crate::Video;
use chrono::Utc;
use regex::Regex;
use serde::Deserialize;
use std::error::Error;

#[derive(Debug, Deserialize)]
struct Data {
    props: Props,
}

#[derive(Debug, Deserialize)]
struct Props {
    pageProps: PageProps,
}

#[derive(Debug, Deserialize)]
struct PageProps {
    instance: PropsInstance,
}

#[derive(Debug, Deserialize)]
struct PropsInstance {
    title: String,
    tags: Vec<String>,
    code: String,
    img_normal: String,
}

pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    println!("fetching video:{}", video_url);

    let reference = String::from(video_url.trim());

    let video_info = get_video_info(video_url, &reference)?;
    let video = video_util::save_video_in_db(&context, video_info).await?;
    if video.published {
        println!("already published.");
        return Ok(());
    }

    let vid = video.vid;
    let (_, ele_content) = browser::fetch_element(video_url, "#__next > div > div:nth-child(4) > div.MuiBox-root.jss23.jss17 > div > script:nth-child(5)",false)?;
    let url_pattern = Regex::new(r#"const source = '([^']+)';"#).unwrap();

    let mut m3u8_url = None;
    if let Some(captures) = url_pattern.captures(&ele_content) {
        // Extract the URL from the captured group
        if let Some(url) = captures.get(1) {
            m3u8_url = Some(url.as_str().to_string());
            // return Some(url.as_str().to_string());
        }
    }

    let m3u8_url = m3u8_url.unwrap();

    let video_id = video.id;
    let m3u8_filename =
        video_util::download_ts(&context, video_id.as_str(), m3u8_url.as_str(), None).await?;

    let out_file = video_util::combine_ts(video_id.as_str(), m3u8_filename.as_str(), "output.mp4")?;
    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id.as_str(), resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;

    // // same cover image url to tmp
    // let client = reqwest::Client::new();

    // let resp = client
    //     .get(&cover_image_url)
    //     .header("User-Agent", context.user_agent.as_str())
    //     .send()
    //     .await?;
    // let webp_data: Vec<u8> = resp.bytes().await?.to_vec();

    // // Create a Cursor from the image bytes
    // let cursor = Cursor::new(webp_data);
    // let dynamic_image = image::load(cursor, image::ImageFormat::WebP).unwrap(); // Adjust the format as needed

    // // Encode the image to JPEG
    // let mut cursor = Cursor::new(Vec::new());
    // dynamic_image.write_to(&mut cursor, image::ImageOutputFormat::Jpeg(90))?;
    // video_util::save_tmp_file(&video_id, "cover.jpg", true, &cursor.into_inner())?;

    // try to search cover image first
    if let Some(vid) = vid {
        if let Some(image_url) = cover::search_get_cover_image(&vid).await? {
            video_util::save_tmp_file_from_url(
                context,
                &video_id,
                &image_url,
                None,
                true,
                false,
                "cover.jpg",
                context.config.http_config.timeout_short,
                false,
            )
            .await?;
        } else {
            video_util::get_cover_image(&resized_file, "cover.jpg", duration)?;
        }
    } else {
        // generate cover image
        video_util::get_cover_image(&resized_file, "cover.jpg", duration)?;
    }

    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    video_util::upload_s3(
        context,
        video_id.as_str(),
        video_source.folder.as_str(),
        resolution,
        true,
    )
    .await?;

    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}

fn get_video_info(video_url: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let data: Data = browser::fetch_jsobject(video_url, "#__NEXT_DATA__", false)?;

    // println!("title:{}", data.props.pageProps.instance.title);
    let title = data.props.pageProps.instance.title;
    let tags = data.props.pageProps.instance.tags;
    let vid = data.props.pageProps.instance.code;
    let cover_url = data.props.pageProps.instance.img_normal;
    let created_at = Utc::now();

    let video = Video {
        name: title,
        description: None,
        cover_image_url: Some(cover_url),
        reference: String::from(reference),
        tags: tags,
        vid: Some(vid),
        created_at: created_at,
        view_count: 29,
    };
    Ok(video)
}
