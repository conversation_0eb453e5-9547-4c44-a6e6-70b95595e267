use crate::actions::cover;
use crate::util::browser;
use crate::util::ffmpeg;
use crate::util::video_util;
use crate::Context;
use crate::Video;
use chrono::{DateTime, TimeZone, Utc};
use scraper::Selector;
use serde::Deserialize;
use std::error::Error;
use url::Url;

#[derive(Debug, Deserialize)]
struct Data {
    single_media_sources: Vec<MediaSource>,
}

#[derive(Debug, Deserialize)]
struct MediaSource {
    source_label: String,
    source_file: String,
}
pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    let resp = browser::fetch_js_and_eval(
        video_url,
        "script#beeteam368_obj_wes-js-extra",
        "vidorev_jav_js_object",
        true,
    )?;
    let html_content = resp.0;
    let data: Data = resp.1;
    // dbg!(&data);

    let mut reference = String::from("");
    if let Ok(video_url_obj) = Url::parse(video_url) {
        reference = String::from(video_url_obj.path());
    }
    let video_info = get_video_info(html_content.as_str(), reference.as_str())?;
    // dbg!(video_info);
    let cover_image_url = video_info.clone().cover_image_url;
    let video = video_util::save_video_in_db(context, video_info).await?;
    if video.published {
        println!("already published.");
        return Ok(());
    }

    let vid = video.vid;
    let mut resolution_to_download = 0;
    let mut download_url = None;
    for media_source in data.single_media_sources {
        let resolution: String = media_source
            .source_label
            .chars()
            .filter(|c| c.is_digit(10))
            .collect();
        let resolution: i32 = resolution.parse()?;
        if resolution <= 1080 && resolution > resolution_to_download {
            resolution_to_download = resolution;
            download_url = Some(String::from(media_source.source_file));
        }
    }
    println!("resolution_to_download:{}", resolution_to_download);

    let download_url = download_url.unwrap();
    println!("download url:{}", download_url);
    let video_id = video.id;
    let m3u8_filename =
        video_util::download_ts(context, video_id.as_str(), download_url.as_str(), None).await?;

    // let out_dir = out_path.to_str().ok_or("can not form output dir")?;
    let out_file = video_util::combine_ts(video_id.as_str(), m3u8_filename.as_str(), "output.mp4")?;
    // video::combine_ts(video_id, "index.m3u8")?;
    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id.as_str(), resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;

    // try to search cover image first
    if let Some(vid) = vid {
        if let Some(image_url) = cover::search_get_cover_image(&vid).await? {
            video_util::save_tmp_file_from_url(
                context,
                &video_id,
                &image_url,
                None,
                true,
                false,
                "cover.jpg",
                context.config.http_config.timeout_short,
                false,
            )
            .await?;
        } else {
            video_util::save_tmp_file_from_url(
                context,
                &video_id,
                &cover_image_url.unwrap(),
                None,
                true,
                false,
                "cover.jpg",
                context.config.http_config.timeout_short,
                false,
            )
            .await?;
            // // generate cover image
            // video_util::get_cover_image(&resized_file, "cover.jpg", duration)?;
        }
    } else {
        // generate cover image
        video_util::get_cover_image(&resized_file, "cover.jpg", duration)?;
    }

    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    video_util::upload_s3(
        context,
        video_id.as_str(),
        video_source.folder.as_str(),
        resolution,
        true,
    )
    .await?;

    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}

fn get_video_info(html_content: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let document = scraper::Html::parse_document(html_content);

    let name_selector = Selector::parse("head > meta[property='og:title']")?;
    let name = document
        .select(&name_selector)
        .next()
        .ok_or("can not fetch name")?
        .value()
        .attr("content")
        .ok_or("can not fetch name")?;
    let name = name.trim_end_matches("- CableAV").trim();
    println!("name:{}", name);

    let image_selector = Selector::parse("head > meta[property='og:image']")?;
    let image_url = document
        .select(&image_selector)
        .next()
        .ok_or("can not fetch image")?
        .value()
        .attr("content")
        .ok_or("can not fetch image")?;
    println!("image_url:{}", image_url);

    let tag_selector =
        Selector::parse("article > header > div.categories-elm.meta-font > div > a")?;
    let tag = document
        .select(&tag_selector)
        .next()
        .unwrap()
        .text()
        .collect::<String>();
    println!("tag:{}", tag);

    let mut tags = Vec::new();
    match tag.as_str() {
        "91大神" => {
            tags.push("探花");
        }
        "中國主播" | "主播福利" | "網紅福利" => {
            tags.push("自拍流出");
            tags.push("主播");
        }
        _ => {
            tags.push(tag.as_str());
        }
    }

    let mut vid = get_vid(name)?;

    if tag.as_str() == "國產AV" {
        tags = get_tags(name)?;
    } else {
        vid = None;
    }

    let mut title = String::from(name);
    if !(tags.contains(&"自拍流出") || tags.contains(&"探花")) {
        title = format_name(name)?;
    }

    let tags = tags.iter().map(|&s| s.to_string()).collect();

    let created_at_selector = Selector::parse("head > meta[property='article:published_time']")?;
    let created_at_str = document
        .select(&created_at_selector)
        .next()
        .unwrap()
        .attr("content")
        .unwrap();
    let mut created_at = Utc::now();

    if let Ok(t) = DateTime::parse_from_rfc3339(created_at_str) {
        created_at = Utc.from_utc_datetime(&t.naive_utc());
    }

    let video = Video {
        name: title,
        description: None,
        cover_image_url: Some(String::from(image_url)),
        reference: String::from(reference),
        tags: tags,
        vid: vid,
        created_at: created_at,
        view_count: 29,
    };
    Ok(video)
}

fn get_tags(title: &str) -> Result<Vec<&str>, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 3 {
        return Ok(segs[..=1].to_vec());
    } else {
        return Ok(segs[..1].to_vec());
    }
}

fn get_vid(title: &str) -> Result<Option<String>, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 3 {
        let mut vid = segs[2];
        if vid == "FLIXKO" {
            vid = segs[3];
        }
        return Ok(Some(String::from(vid)));
    }
    Ok(None)
}

fn format_name(title: &str) -> Result<String, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 4 {
        let title = segs[3..].join(" ");
        return Ok(title);
    } else if segs.len() >= 2 {
        let title = segs[1..].join(" ");
        return Ok(title);
    } else {
        return Ok(String::from(title));
    }
}
