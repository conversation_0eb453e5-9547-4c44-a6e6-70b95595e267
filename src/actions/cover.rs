use crate::actions::{madou, playav, ppp, shise};
use url::Url;

pub async fn search_get_cover_image(
    source: &str,
) -> Result<Option<String>, Box<dyn std::error::Error>> {
    let mut image_url = None;

    if let Ok(_) = Url::parse(&source) {
        if source.contains("madou") {
            image_url = madou::get_cover_image(&source)?;
        } else if source.contains("playav") {
            image_url = playav::get_cover_image(&source).await?;
        } else if source.contains("shise") {
            image_url = shise::get_cover_image(&source, None).await?;
        } else if source.contains("ppp") {
            image_url = ppp::get_cover_image(&source)?;
            // } else if source.contains("huadu") {
            //     image_url = huadu::get_cover_image(&source)?;
        }
    } else {
        image_url = ppp::search_get_cover_image(&source)?;
        if image_url == None {
            image_url = madou::search_get_cover_image(&source)?;
        }
        // if image_url == None {
        //     image_url = huadu::search_get_cover_image(&source)?;
        // }
        // if image_url == None {
        //     image_url = playav::search_get_cover_image(&source).await?;
        // }
    }
    Ok(image_url)
}
