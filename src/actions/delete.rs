use crate::prisma::{source, user_favorite, user_history, video};
use crate::Context;
use chrono::Utc;
use prisma_client_rust::Direction;
use std::error::Error;

pub async fn execute(context: &Context) -> Result<(), Box<dyn Error>> {
    let videos = context
        .db_client
        .video()
        .find_many(vec![video::archived::equals(true)])
        .order_by(video::archived_at::order(Direction::Desc))
        .exec()
        .await?;
    println!("total videos:{}", videos.len());

    for video in videos {
        let archived_at = video.archived_at.unwrap();

        let diff = Utc::now().signed_duration_since(archived_at);

        println!(
            "video:{} archived_at:{}, diff:{}h",
            video.name,
            archived_at,
            diff.num_hours()
        );
        if diff > chrono::Duration::hours(3) {
            println!("deleting video:{}", video.name);
            delete_video(context, video).await?;
        }
    }
    Ok(())
}

async fn delete_video(context: &Context, video: video::Data) -> Result<(), Box<dyn Error>> {
    // deletting s3
    let dir = format!("hls/{}", video.id);
    let files = context.s3_client.list_all_files(dir.as_str(), true).await?;

    for file in files {
        let key = file.key.unwrap();
        println!("deleting file:{}", &key);
        context.s3_client.delete_object(&key).await?;
    }

    // deleting source
    context
        .db_client
        .source()
        .delete_many(vec![source::video_id::equals(video.id.clone())])
        .exec()
        .await?;

    // deleting history
    context
        .db_client
        .user_history()
        .delete_many(vec![user_history::video_id::equals(video.id.clone())])
        .exec()
        .await?;

    // delete favorite
    context
        .db_client
        .user_favorite()
        .delete_many(vec![user_favorite::video_id::equals(video.id.clone())])
        .exec()
        .await?;
    Ok(())
}
