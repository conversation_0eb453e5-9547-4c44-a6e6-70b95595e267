use crate::util::browser;
use scraper::Selector;
use std::error::Error;
use url::Url;

pub fn search_get_cover_image(vid: &str) -> Result<Option<String>, Box<dyn Error>> {
    let search_url = format!(
        "https://huaduys.com/vodsearch/-------------.html?wd={}",
        vid
    );
    let html_content = browser::fetch(&search_url, true)?;

    let document = scraper::Html::parse_document(html_content.as_str());

    let article_selector = Selector::parse(
        "body > div > div > div > div > div > div.stui-pannel_bd > ul > li > div > a",
    )?;
    let article_num = document.select(&article_selector).into_iter().count();

    if article_num != 1 {
        println!("can not find cover image for:{}", vid);
        return Ok(None);
    }
    let video_path = document
        .select(&article_selector)
        .next()
        .unwrap()
        .attr("href")
        .unwrap();

    if let Ok(p) = Url::parse(&search_url) {
        let video_url = format!("{}://{}{}", p.scheme(), p.host_str().unwrap(), video_path);
        println!("video url:{}", video_url);
        return get_cover_image(video_url.as_str());
    }
    Ok(None)
}

pub fn get_cover_image(video_url: &str) -> Result<Option<String>, Box<dyn Error>> {
    let fetch_result = browser::fetch_element(video_url, "body", true)?;
    let html_content = fetch_result.0;

    let document = scraper::Html::parse_document(html_content.as_str());

    // let image_selector = Selector::parse("head > meta[property='og:image']")?;
    // let image_url = document
    //     .select(&image_selector)
    //     .next()
    //     .ok_or("can not fetch image")?
    //     .value()
    //     .attr("content")
    //     .ok_or("can not fetch image")?;
    let image_selector = Selector::parse("body > div > div > div.col-lg-wide-7.col-xs-1.padding-0 > div:nth-child(1) > div > div > div.stui-vodlist__box > a > img")?;
    let image_url = document
        .select(&image_selector)
        .next()
        .ok_or("can not fetch image")?
        .value()
        .attr("data-original")
        .ok_or("can not fetch image")?;
    println!("image_url:{}", image_url);
    Ok(Some(String::from(image_url)))
}
