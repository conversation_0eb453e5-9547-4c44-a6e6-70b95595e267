use crate::util::ffmpeg;
use crate::util::video_util;
use crate::{Context, FetchVideoError};
use std::error::Error;
use std::fs;
use std::path::Path;

pub async fn fetch_video(
    context: &Context,
    video_folder: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    // let entries = fs::read_dir(video_folder)?;

    let video_id = Path::new(video_folder)
        .file_name()
        .unwrap()
        .to_str()
        .unwrap();
    println!("video id:{}", video_id);

    let video_files: Vec<String> = fs::read_dir(video_folder)?
        .filter_map(|e| {
            let file_path = e.as_ref().unwrap().path();
            let file_name = file_path.to_str().unwrap();
            if file_name.ends_with(".mp4") || file_name.ends_with(".mov") {
                return Some(String::from(file_name));
            }
            return None;
        })
        .collect();

    let image_files: Vec<_> = fs::read_dir(video_folder)?
        .filter_map(|e| {
            let file_path = e.as_ref().unwrap().path();
            let file_name = file_path.to_str().unwrap();
            if file_name.ends_with(".jpg") || file_name.ends_with(".jpeg") {
                return Some(String::from(file_name));
            }
            return None;
        })
        .collect();
    // dbg!(video_files);
    // dbg!(image_files);

    if video_files.len() != 1 {
        return Err(Box::new(FetchVideoError(String::from(
            "no video file found",
        ))));
    }

    let video_file: &str = video_files[0].as_ref();
    let out_file = video_util::copy_to_tmp_file(video_id, video_file, "output.mp4")?;

    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id, resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;
    // generate cover image
    if image_files.len() == 1 {
        let image_file: &str = image_files[0].as_ref();
        video_util::copy_to_tmp_file(video_id, image_file, "cover.jpg")?;
    } else {
        video_util::get_cover_image(&resized_file, "cover.jpg", duration)?;
    }

    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    video_util::upload_s3(
        context,
        video_id,
        video_source.folder.as_str(),
        resolution,
        false,
    )
    .await?;

    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}
