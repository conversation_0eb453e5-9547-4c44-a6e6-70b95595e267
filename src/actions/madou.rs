use crate::util::browser;
use regex::Regex;
use scraper::Selector;
use std::error::Error;

pub fn search_get_cover_image(vid: &str) -> Result<Option<String>, Box<dyn Error>> {
    let search_url = format!("https://madou.club/?s={}", vid);
    let html_content = browser::fetch(&search_url, true)?;

    let document = scraper::Html::parse_document(html_content.as_str());

    let article_selector =
        Selector::parse("body > section > div.excerpts-wrapper > div > article > a")?;
    let article_num = document.select(&article_selector).into_iter().count();

    if article_num != 1 {
        println!("can not find cover image for:{}", vid);
        return Ok(None);
    }
    let video_url = document
        .select(&article_selector)
        .next()
        .unwrap()
        .attr("href")
        .unwrap();
    println!("video url:{}", video_url);

    get_cover_image(video_url)
}

pub fn get_cover_image(video_url: &str) -> Result<Option<String>, Box<dyn Error>> {
    let fetch_result = browser::fetch_element(video_url, "body > script", true)?;
    let html_content = fetch_result.0;

    let document = scraper::Html::parse_document(html_content.as_str());

    let first_script_selector = Selector::parse("body > script")?;
    let first_script_ele = document.select(&first_script_selector).next().unwrap();
    let first_script_text = first_script_ele.inner_html();

    // println!("sc:{}", first_script_text);
    for line in first_script_text.split("\n").into_iter() {
        if line.contains("shareimage") {
            let pattern = r#"(?i)shareimage\s*:\s*'([^']+)"#;
            let regex = Regex::new(pattern).expect("Invalid regular expression");

            // Use the regular expression to capture the image URL
            if let Some(captures) = regex.captures(line) {
                let image_url = captures.get(1).map(|m| m.as_str()).unwrap();
                return Ok(Some(String::from(image_url)));
            }
        }
    }
    Ok(None)
}
