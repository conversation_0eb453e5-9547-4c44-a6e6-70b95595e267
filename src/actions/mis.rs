use crate::util::{browser, ffmpeg, html_parse, video_util};
use crate::Context;
use crate::Video;
use chrono::Utc;
use scraper::{Html, Selector};
use std::error::Error;

pub async fn fetch_chn(
    context: &Context,
    url: &str,
    start: usize,
    end: usize,
) -> Result<(), Box<dyn Error>> {
    // let mut page = 1;
    for i in (start..=end).rev() {
        println!("{}", i);
        let url = format!("{}?page={}", url, i);
        fetch_category(context, url.as_str()).await?;
    }
    Ok(())
}

pub async fn fetch_category(context: &Context, url: &str) -> Result<(), Box<dyn Error>> {
    let (html_content, _) = browser::fetch_element(
        url,
        "body > div:nth-child(3) > div > div > div:nth-child(1)",
        false,
    )?;
    // println!("{}", html_content);

    let document = scraper::Html::parse_document(html_content.as_str());

    let videos_selector =
        Selector::parse("body > div > div > div > div > div > div > a:nth-child(1)")?;

    let mut video_urls = document
        .select(&videos_selector)
        .into_iter()
        .map(|e| e.attr("href").unwrap().to_string())
        .collect::<Vec<String>>();

    video_urls.dedup();
    video_urls.retain(|url| url.contains("http"));

    for video_url in video_urls {
        println!("url:{}", video_url);
        fetch_video_with_retry(context, video_url.as_str(), -1, 3).await?;
    }

    Ok(())
}
pub async fn fetch_video_with_retry(
    context: &Context,
    video_url: &str,
    rotate: i8,
    max_attempts: u32,
) -> Result<(), Box<dyn Error>> {
    let mut attempts = 0;
    loop {
        attempts += 1;
        match fetch_video(context, video_url, rotate).await {
            Ok(_) => return Ok(()),
            Err(e) => {
                if attempts >= max_attempts {
                    return Err(Box::new(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("Failed to upload image after {} attempts: {}", e, attempts),
                    )));
                }
                println!("Attempt {} failed: {}, retrying...", attempts, e);
            }
        }
    }
}

pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    let resp = browser::fetch_js_and_eval_str(
        video_url,
        "body > script:nth-child(8)",
        "window.player.source",
        false,
    )?;
    let html_content = resp.0;
    let mut download_url = resp.1;

    println!("m3u8 url before:{}", &download_url);
    if !download_url.contains("720p") {
        download_url = update_resolution(&download_url).unwrap();
        println!("m3u8 url after:{}", &download_url);
    }

    let reference = String::from(video_url.trim());
    let video_info = get_video_info(html_content.as_str(), reference.as_str())?;
    // dbg!(&video_info);
    let cover_image_url = video_info.clone().cover_image_url;
    let video = video_util::save_video_in_db(context, video_info).await?;
    if video.published {
        println!("already published.");
        return Ok(());
    }

    let video_id = video.id;
    let m3u8_filename =
        video_util::download_ts(context, video_id.as_str(), download_url.as_str(), None).await?;

    // let out_dir = out_path.to_str().ok_or("can not form output dir")?;
    let out_file = video_util::combine_ts(video_id.as_str(), m3u8_filename.as_str(), "output.mp4")?;
    // video::combine_ts(video_id, "index.m3u8")?;
    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id.as_str(), resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;

    video_util::save_tmp_file_from_url(
        context,
        &video_id,
        &cover_image_url.unwrap(),
        None,
        true,
        false,
        "cover.jpg",
        context.config.http_config.timeout_short,
        false,
    )
    .await?;

    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    video_util::upload_s3(
        context,
        video_id.as_str(),
        video_source.folder.as_str(),
        resolution,
        true,
    )
    .await?;

    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}

fn get_video_info(html_content: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let document = scraper::Html::parse_document(html_content);

    let name_selector = Selector::parse("head > meta[property='og:title']")?;
    let name = document
        .select(&name_selector)
        .next()
        .ok_or("can not fetch name")?
        .value()
        .attr("content")
        .ok_or("can not fetch name")?;
    let name = name.trim_end_matches("- CableAV").trim();
    println!("name:{}", name);

    let description_selector = Selector::parse("head > meta[property='og:description']")?;
    let description = document
        .select(&description_selector)
        .next()
        .ok_or("can not fetch description")?
        .value()
        .attr("content")
        .ok_or("can not fetch description")?;
    println!("description:{}", description);

    let title = format_name(name)?;

    let vid = get_vid(name)?;

    let image_selector = Selector::parse("head > meta[property='og:image']")?;
    let image_url = document
        .select(&image_selector)
        .next()
        .ok_or("can not fetch image")?
        .value()
        .attr("content")
        .ok_or("can not fetch image")?;
    println!("image_url:{}", image_url);

    // Robustly extract tags from the '類型:' section
    let div_selector = Selector::parse("div.text-secondary").unwrap();
    let span_selector = Selector::parse("span").unwrap();
    let a_selector = Selector::parse("a").unwrap();

    let mut video_tags = Vec::new();
    for div in document.select(&div_selector) {
        let has_type_label = div
            .select(&span_selector)
            .any(|span| span.text().any(|t| t.trim() == "類型:"));
        if has_type_label {
            for a in div.select(&a_selector) {
                let tag = a.text().collect::<String>().trim().to_string();
                if !tag.is_empty() {
                    video_tags.push(tag);
                }
            }
            break;
        }
    }
    // Remove unwanted tags if present
    video_tags.retain(|tag| tag != "4K" && tag != "高清" && tag != "獨家");
    // Prepend "日本" to the tag list
    video_tags.insert(0, String::from("日本"));
    println!("tags:{:?}", video_tags);

    let created_at = Utc::now();

    let video = Video {
        name: title,
        description: Some(String::from(description)),
        cover_image_url: Some(String::from(image_url)),
        reference: String::from(reference),
        tags: video_tags,
        vid: vid,
        created_at: created_at,
        view_count: 29,
    };
    // dbg!(&video);
    Ok(video)
}

fn get_tags(title: &str) -> Result<Vec<&str>, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 2 {
        return Ok(vec![segs[segs.len() - 1]]);
    } else {
        return Ok([].to_vec());
    }
}

fn get_vid(title: &str) -> Result<Option<String>, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 3 {
        let mut vid = segs[0].trim();
        return Ok(Some(String::from(vid)));
    }
    Ok(None)
}

fn format_name(title: &str) -> Result<String, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 2 {
        let title = segs[1..].join(" ");
        return Ok(title);
    } else {
        return Ok(String::from(title));
    }
}

fn update_resolution(url: &str) -> Option<String> {
    // Parse the URL
    let mut parsed_url = url::Url::parse(url).unwrap();

    // Check if the domain is as expected
    if parsed_url.domain() != Some("surrit.com") {
        return None;
    }

    // Get the path of the URL
    parsed_url
        .path_segments_mut()
        .map_err(|_| "can not parse")
        .unwrap()
        .pop()
        .pop()
        .push("1280x720")
        // .push("842x480")
        .push("video.m3u8");

    // Reconstruct the URL
    let new_url = parsed_url.to_string();

    Some(new_url)
}
