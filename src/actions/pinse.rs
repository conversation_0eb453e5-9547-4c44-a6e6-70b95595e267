use crate::util::{browser, ffmpeg, translate, video_util};
use crate::Context;
use crate::Video;
use chrono::Utc;
use regex::Regex;
use scraper::{Html, Selector};
use std::error::Error;

pub async fn fetch_chn(
    context: &Context,
    url: &str,
    start: usize,
    end: usize,
) -> Result<(), Box<dyn Error>> {
    // let mut page = 1;
    for i in (start..=end).rev() {
        println!("{}", i);
        let url = format!("{}?page={}", url, i);
        fetch_category(context, url.as_str()).await?;
    }
    Ok(())
}

pub async fn fetch_category(context: &Context, url: &str) -> Result<(), Box<dyn Error>> {
    let (html_content, _) =
        browser::fetch_element(url, "body > main > div > div:nth-child(1) > div > a", false)?;

    let document = scraper::Html::parse_document(html_content.as_str());

    let videos_selector = Selector::parse("body > main > div > div > div.group > a")?;

    let mut video_urls = document
        .select(&videos_selector)
        .into_iter()
        .map(|e| e.attr("href").unwrap().to_string())
        .collect::<Vec<String>>();

    video_urls.dedup();

    for video_url in video_urls {
        let parsed_url = url::Url::parse(url)?;
        let domain = parsed_url.host_str().ok_or("can not fetch domain")?;
        let url = format!("{}://{}{}", parsed_url.scheme(), domain, video_url);
        println!("url:{}", url);
        fetch_video(context, url.as_str(), -1).await?;
    }

    Ok(())
}

pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    let reference = String::from(video_url.trim());

    // need to get the iframe first
    let resp = browser::fetch_element_attr(video_url, "iframe", "src", true).await?;
    let html_content = resp.0;
    let iframe_element = resp.1;

    // get iframe url
    let video_doc = scraper::Html::parse_document(&html_content);
    let iframe_selector = Selector::parse("iframe")?;
    let iframe_url = video_doc
        .select(&iframe_selector)
        .next()
        .ok_or("can not fetch name")?
        .value()
        .attr("src")
        .ok_or("can not fetch name")?;
    println!("iframe url: {}", iframe_url);

    let video_info = get_video_info(html_content.as_str(), reference.as_str()).await?;

    let resp = browser::fetch_element_attr(iframe_url, "source", "src", true).await?;
    let iframe_content = resp.0;
    let download_url = resp.1;

    let iframe_doc = scraper::Html::parse_document(&iframe_content);
    let video_selector = Selector::parse("source")?;
    let download_url = iframe_doc
        .select(&video_selector)
        .next()
        .ok_or("can not fetch name")?
        .value()
        .attr("src")
        .ok_or("can not fetch name")?;
    println!("download url: {}", download_url);

    // Save video to database
    let video = video_util::save_video_in_db(context, video_info.clone()).await?;

    if video.published {
        println!("already published.");
        return Ok(());
    }

    let video_id = video.id;

    // Process video
    let out_file = video_util::save_tmp_file_from_url(
        context,
        video_id.as_str(),
        download_url,
        None,
        false,
        false,
        "output.mp4",
        context.config.http_config.timeout_long,
        true,
    )
    .await?;
    let resize_resp = video_util::resize_video(&out_file, "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    // Save video source
    let video_source =
        video_util::save_video_source_in_db(context, &video_id, resolution as i32).await?;

    let duration = ffmpeg::get_video_duration(&resized_file)?;
    // generate cover image
    video_util::get_cover_image(&resized_file, "cover.jpg", duration)?;

    // Generate thumbnails
    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    // Upload to S3
    video_util::upload_s3(
        context,
        &video_id,
        video_source.folder.as_str(),
        resolution,
        false,
    )
    .await?;

    // Publish video
    video_util::publish_video(context, &video_id, duration).await?;

    Ok(())
}

pub async fn get_video_info(html_content: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let document = scraper::Html::parse_document(html_content);

    // Extract name from og:title
    let name_selector = Selector::parse("head > meta[property='og:title']")?;
    let name = document
        .select(&name_selector)
        .next()
        .ok_or("can not fetch name")?
        .value()
        .attr("content")
        .ok_or("can not fetch name")?;
    let name = name.trim();

    let mut video_tags = Vec::new();
    video_tags.insert(0, String::from("自拍流出"));

    // Process title to get vid and formatted name
    let title = format_name(name).await?;

    let created_at = Utc::now();

    Ok(Video {
        name: title,
        description: None,
        cover_image_url: None,
        reference: String::from(reference),
        tags: video_tags,
        vid: None,
        created_at,
        view_count: 29,
    })
}

async fn format_name(title: &str) -> Result<String, Box<dyn Error>> {
    let title = title.replace(" - 91PinSe", "");
    let title = title.as_str();

    // Remove content between parentheses
    let re = Regex::new(r"\([^()]*\)")?;
    let result = re.replace_all(title, "");

    let re = Regex::new(r"【[^【】]*】")?;
    let result = re.replace_all(&result, "");

    let re = Regex::new(r"（[^（）]*）")?;
    let result = re.replace_all(&result, "");

    // Remove content between brackets
    let re = Regex::new(r"\[[^\[\]]*\]")?;
    let result = re.replace_all(&result, "");

    // Convert Simplified Chinese to Traditional Chinese using Google Translate
    let result = translate::to_traditional(&result).await?;

    // Trim any extra whitespace and return
    Ok(result.trim().to_string())
}
