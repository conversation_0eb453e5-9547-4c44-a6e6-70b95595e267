use crate::actions::cover;
use crate::prisma::video::vid;
use crate::util::browser;
use crate::util::ffmpeg;
use crate::util::video_util;
use crate::Context;
use crate::Video;
use chrono::Utc;
use regex::Regex;
use scraper::Selector;
use std::env;
use std::error::Error;
use std::fs::{self, File};
use std::io::Write;
use std::path::Path;
use url::Url;

pub async fn search_get_cover_image(vid: &str) -> Result<Option<String>, Box<dyn Error>> {
    let search_url = format!("https://playav.xyz/?s={}", vid);
    let html_content = browser::fetch(&search_url, true)?;

    let document = scraper::Html::parse_document(html_content.as_str());

    let article_selector = Selector::parse("#main > div > article > a")?;
    let article_num = document.select(&article_selector).into_iter().count();

    if article_num != 1 {
        println!("can not find cover image for:{}", vid);
        return Ok(None);
    }
    let video_url = document
        .select(&article_selector)
        .next()
        .unwrap()
        .attr("href")
        .unwrap();

    if let Ok(p) = Url::parse(&search_url) {
        // let video_url = format!("{}://{}{}", p.scheme(), p.host_str().unwrap(), video_path);
        println!("video url:{}", video_url);
        return get_cover_image(video_url).await;
    }
    Ok(None)
}

pub async fn get_cover_image(video_url: &str) -> Result<Option<String>, Box<dyn Error>> {
    let fetch_response = browser::fetch_element(video_url, ".responsive-player > iframe", true)?;
    let html_content = fetch_response.0;

    let document = scraper::Html::parse_document(html_content.as_str());

    let image_selector = Selector::parse("head > meta[property='og:image']")?;
    let cover_image_url = document
        .select(&image_selector)
        .next()
        .ok_or("can not fetch image")?
        .value()
        .attr("content")
        .ok_or("can not fetch image")?;
    println!("image_url:{}", cover_image_url);

    // let iframe_content = fetch_response.1;

    // let iframe_document = scraper::Html::parse_document(&iframe_content);
    // let iframe_selector = Selector::parse("iframe")?;
    // let video_source_url = iframe_document
    //     .select(&iframe_selector)
    //     .next()
    //     .unwrap()
    //     .attr("src")
    //     .unwrap();

    // println!("source url:{}", video_source_url);

    // let fetch_response =
    //     browser::fetch_element_attr(video_source_url, "#video_player_html5_api", "src", true)
    //         .await?;
    // let html_content = fetch_response.0;

    // let html_document = scraper::Html::parse_document(&html_content);
    // let video_selector = Selector::parse("#video_player_html5_api")?;
    // let video_ele = html_document.select(&video_selector).next().unwrap();
    // let cover_image_url = video_ele.attr("poster").unwrap().to_string();
    Ok(Some(cover_image_url.to_string()))
}

pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    let fetch_response = browser::fetch_element(video_url, ".responsive-player > iframe", true)?;
    let html_content = fetch_response.0;
    let iframe_content = fetch_response.1;

    // let mut reference = String::from("");
    let mut reference = None;
    if let Ok(video_url_obj) = Url::parse(video_url) {
        reference = Some(String::from(video_url_obj.path()));
    }

    let reference = reference.unwrap();
    let video_info = get_video_info(html_content.as_str(), reference.as_str())?;
    // dbg!(video_info);
    let vid = video_info.clone().vid;
    let video = video_util::save_video_in_db(context, video_info).await?;
    if video.published {
        println!("already published.");
        return Ok(());
    }

    let iframe_document = scraper::Html::parse_document(&iframe_content);
    let iframe_selector = Selector::parse("iframe")?;
    let video_source_url = iframe_document
        .select(&iframe_selector)
        .next()
        .unwrap()
        .attr("src")
        .unwrap();

    println!("source url:{}", video_source_url);

    let fetch_response =
        browser::fetch_element_attr(video_source_url, "#video_player_html5_api", "src", true)
            .await?;
    let html_content = fetch_response.0;

    let html_document = scraper::Html::parse_document(&html_content);
    let video_selector = Selector::parse("#video_player_html5_api")?;
    let video_ele = html_document.select(&video_selector).next().unwrap();
    let video_download_url = video_ele.attr("src").unwrap();
    let mut cover_image_url = video_ele.attr("poster").unwrap().to_string();
    // try to search cover image first
    if let Some(vid) = vid {
        if let Some(image_url) = cover::search_get_cover_image(&vid).await? {
            cover_image_url = image_url.clone();
        }
    }
    println!("download url:{}", video_download_url);
    println!("cover image url:{}", cover_image_url);

    let video_id = video.id;
    // download video file
    let out_file = save_tmp_file_from_url(
        context,
        video_id.as_str(),
        video_download_url,
        true,
        "output.mp4",
    )
    .await?;

    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id.as_str(), resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;

    // same cover image url to tmp
    video_util::save_tmp_file_from_url(
        context,
        video_id.as_str(),
        &cover_image_url,
        None,
        true,
        false,
        "cover.jpg",
        context.config.http_config.timeout_short,
        false,
    )
    .await?;

    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    video_util::upload_s3(
        context,
        video_id.as_str(),
        video_source.folder.as_str(),
        resolution,
        true,
    )
    .await?;

    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}

fn get_video_info(html_content: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let document = scraper::Html::parse_document(html_content);

    let name_selector = Selector::parse("head > meta[property='og:title']")?;
    let name = document
        .select(&name_selector)
        .next()
        .ok_or("can not fetch name")?
        .value()
        .attr("content")
        .ok_or("can not fetch name")?;
    println!("name:{}", name);

    // let image_selector = Selector::parse("head > meta[property='og:image']")?;
    // let image_url = document
    //     .select(&image_selector)
    //     .next()
    //     .ok_or("can not fetch image")?
    //     .value()
    //     .attr("content")
    //     .ok_or("can not fetch image")?;
    // println!("image_url:{}", image_url);

    let mut vid = get_vid(name)?;
    let mut title = String::from(name);

    let tag_selector =
        Selector::parse("main > article > div.entry-content > div.video-player-area.with-sidebar-ads > div.video-tags > div > a")?;
    let tags_listed: Vec<String> = document
        .select(&tag_selector)
        .into_iter()
        .map(|e| e.text().collect())
        .collect();
    let tags_listed: Vec<String> = tags_listed
        .into_iter()
        .map(|e| String::from(e.trim()))
        .collect();

    let mut tags = Vec::new();
    if tags_listed.contains(&"N炮房".to_string()) {
        tags = vec!["探花"];
        vid = None;
    } else {
        tags = get_tags(name)?;
        title = format_name(name)?;
    }
    // dbg!(tags);
    let tags = tags.iter().map(|&s| s.to_string()).collect();

    let created_at = Utc::now();

    let video = Video {
        name: title,
        description: None,
        cover_image_url: None,
        reference: String::from(reference),
        tags: tags,
        vid: vid,
        created_at: created_at,
        view_count: 29,
    };
    Ok(video)
}

// extract cover image url from style attribute of the div
fn extract_url(input: &str) -> Option<String> {
    // Define a regex pattern to match URLs
    let re = Regex::new(r#"url\(["']?(.*?)["']?\)"#).unwrap();

    // Search for the pattern in the input string
    if let Some(captures) = re.captures(input) {
        // Extract the captured URL
        if let Some(url) = captures.get(1) {
            return Some(url.as_str().to_string());
        }
    }

    None
}

fn get_tags(title: &str) -> Result<Vec<&str>, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() > 2 {
        return Ok(segs[..=1].to_vec());
    } else {
        return Ok(segs[..1].to_vec());
    }
}

fn get_vid(title: &str) -> Result<Option<String>, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 3 {
        let mut vid = segs[2];
        if vid == "FLIXKO" {
            vid = segs[3];
        }
        return Ok(Some(String::from(vid)));
    }
    Ok(None)
}

fn format_name(title: &str) -> Result<String, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 4 {
        let title = segs[3..].join(" ");
        return Ok(title);
    } else if segs.len() >= 2 {
        let title = segs[1..].join(" ");
        return Ok(title);
    } else {
        return Ok(String::from(title));
    }
}

async fn save_tmp_file_from_url(
    context: &Context,
    video_id: &str,
    url: &str,
    use_sub_folder: bool,
    file_name: &str,
) -> Result<String, Box<dyn Error>> {
    let movie_cache_dir = env::var("MOVIE_CACHE_DIR")?;
    // Compose the file path
    let mut path = Path::new(movie_cache_dir.as_str()).join(video_id);
    if use_sub_folder {
        path = path.join("output");
    }
    path = path.join(file_name);

    println!("file_path :{:?}", path);

    // Check if the file exists and has a non-zero size
    if let Ok(metadata) = fs::metadata(&path) {
        if metadata.is_file() && metadata.len() > 0 {
            println!("File '{:?}' already exists and has a non-zero size.", &path);
            return Ok(path.to_str().unwrap().to_string());
        }
    }
    // Create the directory if it doesn't exist
    fs::create_dir_all(path.parent().unwrap())?;

    // let file_path: String = path.to_string_lossy().into();
    let mut file = File::create(&path)?;

    println!("going to download:{}", url);

    // Create custom headers
    let client = reqwest::Client::new();

    let resp = client
        .get(url)
        .header("User-Agent", context.config.user_agent.as_str())
        .header("Referer", "https://dood.re/")
        .header("Connection", "keep-alive")
        .header("Range", "bytes=0-")
        .send()
        .await?;
    let content = resp.bytes().await?;
    file.write_all(&content)?;

    Ok(path.to_str().unwrap().to_string())
}
