use crate::actions::cover;
use crate::util::browser;
use crate::util::ffmpeg;
use crate::util::video_util;
use crate::Context;
use crate::Video;
use chrono::{DateTime, TimeZone, Utc};
use scraper::Selector;
use std::error::Error;

pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    let resp = browser::fetch_js_and_eval_str(
        video_url,
        "body > div.off-canvas.off-canvas--full-width.off-canvas--navigation.js-off-canvas.js-off-canvas--nav > main > section > script:nth-child(4)",
        "stream",
        false,
    )?;
    let html_content = resp.0;
    let data = resp.1;
    // dbg!(&data);

    let reference = String::from(video_url.trim());
    let video_info = get_video_info(html_content.as_str(), reference.as_str())?;
    dbg!(&video_info);
    let cover_image_url = video_info.clone().cover_image_url;
    let video = video_util::save_video_in_db(context, video_info).await?;
    if video.published {
        println!("already published.");
        return Ok(());
    }
    // dbg!(&video);

    let vid = video.vid;
    let download_url = data;
    println!("download url:{}", download_url);
    let video_id = video.id;
    let m3u8_filename =
        video_util::download_ts(context, video_id.as_str(), download_url.as_str(), None).await?;

    // let out_dir = out_path.to_str().ok_or("can not form output dir")?;
    let out_file = video_util::combine_ts(video_id.as_str(), m3u8_filename.as_str(), "output.mp4")?;
    // video::combine_ts(video_id, "index.m3u8")?;
    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id.as_str(), resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;

    // try to search cover image first
    if let Some(vid) = vid {
        if let Some(image_url) = cover::search_get_cover_image(&vid).await? {
            video_util::save_tmp_file_from_url(
                context,
                &video_id,
                &image_url,
                None,
                true,
                false,
                "cover.jpg",
                context.config.http_config.timeout_short,
                false,
            )
            .await?;
        } else {
            video_util::save_tmp_file_from_url(
                context,
                &video_id,
                &cover_image_url.unwrap(),
                None,
                true,
                false,
                "cover.jpg",
                context.config.http_config.timeout_short,
                false,
            )
            .await?;
            // // generate cover image
            // video_util::get_cover_image(&resized_file, "cover.jpg", duration)?;
        }
    } else {
        video_util::save_tmp_file_from_url(
            context,
            &video_id,
            &cover_image_url.unwrap(),
            None,
            true,
            false,
            "cover.jpg",
            context.config.http_config.timeout_short,
            false,
        )
        .await?;
    }

    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    video_util::upload_s3(
        context,
        video_id.as_str(),
        video_source.folder.as_str(),
        resolution,
        true,
    )
    .await?;

    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}

fn get_video_info(html_content: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let document = scraper::Html::parse_document(html_content);

    let name_selector = Selector::parse("head > meta[property='og:title']")?;
    let name = document
        .select(&name_selector)
        .next()
        .ok_or("can not fetch name")?
        .value()
        .attr("content")
        .ok_or("can not fetch name")?;
    let name = name.trim_end_matches("- CableAV").trim();
    println!("name:{}", name);

    let image_selector = Selector::parse("head > meta[property='og:image']")?;
    let image_url = document
        .select(&image_selector)
        .next()
        .ok_or("can not fetch image")?
        .value()
        .attr("content")
        .ok_or("can not fetch image")?;
    println!("image_url:{}", image_url);

    let tags_selector = Selector::parse("head > meta[property='video:tag']")?;
    let meta_tags = document
        .select(&tags_selector)
        .into_iter()
        .map(|e| e.attr("content").unwrap())
        .collect::<Vec<&str>>();
    // dbg!(&tags);
    let tags_selector = Selector::parse(
        "main > div.container.max-width-md > section > div.content-details > div > div > div > a",
    )?;
    let video_tags = document
        .select(&tags_selector)
        .into_iter()
        .map(|e| e.text().collect::<String>())
        .collect::<Vec<String>>();

    let mut vid = None;
    let mut title = String::from(name);
    let mut tags = Vec::new();
    if meta_tags.contains(&"素人自拍") {
        tags.push("自拍流出");
    } else if meta_tags.contains(&"中國AV") {
        tags.push("國產AV");
        vid = get_vid(name)?;
        title = format_name(title.as_str())?;
    } else if meta_tags.contains(&"日本") {
        tags.push("日本");
        vid = get_vid(name)?;
        title = format_name(title.as_str())?;
    }

    // add the last meta tags to tags
    if let Some(&item) = meta_tags.last() {
        if item == "onlyfans" {
            tags.push("OnlyFans");
        } else {
            tags.push(item);
        }
    }

    // add the last video tag to video title
    if let Some(item) = video_tags.last() {
        title = format!("{} {}", title, item);
    }

    let created_at_selector = Selector::parse("head > meta[property='video:release_date']")?;
    let created_at_str = document
        .select(&created_at_selector)
        .next()
        .unwrap()
        .attr("content")
        .unwrap();
    let mut created_at = Utc::now();

    if let Ok(t) = DateTime::parse_from_rfc3339(created_at_str) {
        created_at = Utc.from_utc_datetime(&t.naive_utc());
    }

    let tags = tags.iter().map(|&s| s.to_string()).collect();
    let video = Video {
        name: title,
        description: None,
        cover_image_url: Some(String::from(image_url)),
        reference: String::from(reference),
        tags: tags,
        vid: vid,
        created_at: created_at,
        view_count: 29,
    };
    Ok(video)
}

fn get_vid(title: &str) -> Result<Option<String>, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 1 {
        let vid = segs[0].trim();
        return Ok(Some(String::from(vid)));
    }
    Ok(None)
}

fn format_name(title: &str) -> Result<String, Box<dyn Error>> {
    let segs = title.split(" ").collect::<Vec<&str>>();
    if segs.len() >= 2 {
        let title = segs[1..].join(" ");
        return Ok(title);
    } else {
        return Ok(String::from(title));
    }
}

pub fn search_get_cover_image(vid: &str) -> Result<Option<String>, Box<dyn Error>> {
    let search_url = format!("https://ppp.porn/search/{}/", vid);
    let html_content = browser::fetch(&search_url, true)?;

    let document = scraper::Html::parse_document(html_content.as_str());

    let article_selector = Selector::parse(
        "#list_videos_videos_list_search_result_items > div > div.card-video__img > figure > a",
    )?;
    let article_num = document.select(&article_selector).into_iter().count();

    if article_num != 1 {
        println!("can not find cover image for:{}", vid);
        return Ok(None);
    }
    let video_url = document
        .select(&article_selector)
        .next()
        .unwrap()
        .attr("href")
        .unwrap();
    println!("video url:{}", video_url);

    get_cover_image(video_url)
}

pub fn get_cover_image(video_url: &str) -> Result<Option<String>, Box<dyn Error>> {
    let resp = browser::fetch_js_and_eval_str(
        video_url,
        "body > div.off-canvas.off-canvas--full-width.off-canvas--navigation.js-off-canvas.js-off-canvas--nav > main > section > script:nth-child(4)",
        "stream",
        false,
    )?;
    let html_content = resp.0;
    // dbg!(&data);

    let reference = String::from(video_url.trim());
    let video_info = get_video_info(html_content.as_str(), reference.as_str())?;

    Ok(video_info.cover_image_url)
}
