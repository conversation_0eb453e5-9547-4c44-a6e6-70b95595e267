use crate::actions::cover;
use crate::util::browser;
use crate::util::ffmpeg;
use crate::util::translate;
use crate::util::video_util;
use crate::Context;
use crate::Video;
use chrono::Utc;
use regex;
use scraper::Selector;
use std::error::Error;
use std::path::Path;
pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    let resp = browser::fetch_element(
        video_url,
        "body > div.non-home.player > div.body > div.section.player-container > div:nth-child(1) > script",
        false,
    )?;
    let html_content = resp.0;
    let data = resp.1;

    // Extract m3u8 URL
    let re = regex::Regex::new(r"src: '([^']+)',poster: '([^']+)")?;
    let caps = re.captures(&data).ok_or("Failed to parse video URLs")?;
    let download_url = caps.get(1).ok_or("M3U8 URL not found")?.as_str();
    let cover_image_url = get_cover_image(video_url, Some(&data))
        .await?
        .ok_or("Failed to get cover image")?;
    println!("M3U8: {}, Poster: {}", download_url, cover_image_url);

    let reference = String::from(video_url.trim());
    let video_info = get_video_info(html_content.as_str(), reference.as_str()).await?;
    // dbg!(&video_info);
    // let cover_image_url = video_info.clone().cover_image_url;
    let video = video_util::save_video_in_db(context, video_info).await?;
    if video.published {
        println!("already published.");
        return Ok(());
    }
    // dbg!(&video);

    let video_id = video.id;
    let m3u8_filename =
        video_util::download_ts(context, video_id.as_str(), download_url, Some(video_url)).await?;

    // let out_dir = out_path.to_str().ok_or("can not form output dir")?;
    let out_file = video_util::combine_ts(video_id.as_str(), m3u8_filename.as_str(), "output.mp4")?;
    // video::combine_ts(video_id, "index.m3u8")?;
    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id.as_str(), resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;

    let image_url = if let Some(vid) = video.vid {
        cover::search_get_cover_image(&vid)
            .await?
            .unwrap_or(cover_image_url.to_string())
    } else {
        cover_image_url.to_string()
    };

    let extension = if image_url == cover_image_url {
        "webp"
    } else {
        "jpg"
    };

    video_util::save_tmp_file_from_url(
        context,
        &video_id,
        &image_url,
        Some(video_url),
        true,
        false,
        &format!("cover.{}", extension),
        context.config.http_config.timeout_short,
        false,
    )
    .await?;

    if extension == "webp" {
        // read cover.webp conver to jpg and save back, the folder of cover.webp is same as out_file
        let out_file_dir = Path::new(&out_file).parent().unwrap();
        let cover_image = image::open(out_file_dir.join("cover.webp"))?;
        let cover_image = cover_image.to_rgb8();
        cover_image.save(out_file_dir.join("cover.jpg"))?;
    }

    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    video_util::upload_s3(
        context,
        video_id.as_str(),
        video_source.folder.as_str(),
        resolution,
        true,
    )
    .await?;

    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}

async fn get_video_info(html_content: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let document = scraper::Html::parse_document(html_content);

    let name_selector =
        Selector::parse("body > div.non-home.player > div.body > div > div > div.title > a > h1")?;
    let name: String = document
        .select(&name_selector)
        .next()
        .ok_or("can not fetch name")?
        .text()
        .collect();

    // println!("name:{}", name);
    let name = format_name(name.trim()).await?;

    let tags_selector =
        Selector::parse("body > div.non-home.player > div.body > div > div > div.tags > a > div")?;
    let video_tags = document
        .select(&tags_selector)
        .into_iter()
        .map(|e| e.text().collect::<String>())
        .collect::<Vec<String>>();

    let created_at = Utc::now();

    let video = Video {
        name: name.to_string(),
        description: None,
        cover_image_url: None,
        reference: String::from(reference),
        tags: video_tags,
        vid: None,
        created_at: created_at,
        view_count: 29,
    };
    Ok(video)
}

pub async fn get_cover_image(
    video_url: &str,
    prefetched_data: Option<&str>,
) -> Result<Option<String>, Box<dyn Error>> {
    let data = if let Some(data) = prefetched_data {
        data.to_string()
    } else {
        browser::fetch_element(
            video_url,
            "body > div.non-home.player > div.body > div.section.player-container > div:nth-child(1) > script",
            false,
        )?.1
    };

    // Extract poster URL using regex
    let re = regex::Regex::new(r"src: '([^']+)',poster: '([^']+)")?;
    if let Some(caps) = re.captures(&data) {
        if let Some(poster_match) = caps.get(2) {
            return Ok(Some(poster_match.as_str().to_string()));
        }
    }

    Ok(None)
}

async fn format_name(title: &str) -> Result<String, Box<dyn Error>> {
    // Convert Simplified Chinese to Traditional Chinese using Google Translate
    let result = translate::to_traditional(title).await?;

    // Trim any extra whitespace and return
    Ok(result.trim().to_string())
}
