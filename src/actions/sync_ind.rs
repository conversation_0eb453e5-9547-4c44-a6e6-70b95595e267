use crate::util::meilisearch::{MSVideo, MSVideoSource};
use crate::{prisma::video, Context};
use character_converter::traditional_to_simplified;
use prisma_client_rust::Direction;
use std::error::Error;

pub async fn execute(context: &Context) -> Result<(), Box<dyn Error>> {
    let page_size: i64 = 1000;
    let mut skip = 0;

    loop {
        let videos = context
            .db_client
            .video()
            .find_many(vec![])
            .order_by(video::created_at::order(Direction::Desc))
            .skip(skip)
            .take(page_size)
            .include(video::include!({ sources }))
            .exec()
            .await?;

        if videos.is_empty() {
            break;
        }

        for video in videos {
            if video.published && !video.archived {
                println!("index video:{}", video.name);

                let tags_zh = video
                    .tags
                    .iter()
                    .map(|s| traditional_to_simplified(s.as_str()).to_string())
                    .collect();

                let sources = video
                    .sources
                    .iter()
                    .map(|s| MSVideoSource {
                        resolution: s.resolution,
                    })
                    .collect();

                let video_to_save = MSVideo {
                    id: video.id.clone(),
                    vid: video.vid,
                    name: video.name.clone(),
                    name_zh: traditional_to_simplified(video.name.as_str()).to_string(),
                    duration: video.duration,
                    view_count: video.view_count,
                    tags: video.tags,
                    tags_zh,
                    created_at: video.created_at,
                    sources,
                };

                context.ms_client.save_video(video_to_save).await?;
            } else {
                println!("delete video index:{}", video.name);
                context.ms_client.delete_video(&video.id).await?;
            }
        }

        skip += page_size;
    }

    Ok(())
}
