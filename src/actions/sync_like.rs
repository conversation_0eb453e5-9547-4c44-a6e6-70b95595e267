use crate::{
    prisma::{user_favorite, video},
    Context,
};
use prisma_client_rust::Direction;
use std::error::Error;

pub async fn execute(context: &Context) -> Result<(), Box<dyn Error>> {
    let page_size: i64 = 1000;
    let mut skip = 0;

    loop {
        let videos = context
            .db_client
            .video()
            .find_many(vec![video::archived::equals(false)])
            .order_by(video::created_at::order(Direction::Desc))
            .skip(skip)
            .take(page_size)
            .exec()
            .await?;

        if videos.is_empty() {
            break;
        }

        for video in videos {
            let count = context
                .db_client
                .user_favorite()
                .count(vec![user_favorite::video_id::equals(video.id.clone())])
                .exec()
                .await?;

            println!("video:{} likes:{}", video.name, count);

            context
                .db_client
                .video()
                .update(
                    video::id::equals(video.id),
                    vec![video::like_count::set(count as i32)],
                )
                .exec()
                .await?;
        }

        skip += page_size;
    }
    Ok(())
}
