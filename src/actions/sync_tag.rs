use crate::prisma::{tag, video};
use crate::Context;
use prisma_client_rust::Direction;
use std::collections::HashMap;
use std::error::Error;

pub async fn execute(context: &Context) -> Result<(), Box<dyn Error>> {
    let page_size: i64 = 1000;
    let mut skip = 0;
    let mut tags_map: HashMap<String, i32> = HashMap::new();

    loop {
        let videos = context
            .db_client
            .video()
            .find_many(vec![video::archived::equals(false)])
            .order_by(video::created_at::order(Direction::Desc))
            .skip(skip)
            .take(page_size)
            .exec()
            .await?;

        if videos.is_empty() {
            break;
        }

        // Process videos and count tags
        for video in videos {
            for tag in video.tags {
                tags_map.entry(tag).and_modify(|e| *e += 1).or_insert(1);
            }
        }

        skip += page_size;
    }

    // Process and save tags
    for (tag_id, tag_count) in tags_map {
        println!("tag: {} count: {:?}", tag_id, tag_count);
        if tag_count < 3 {
            println!("skip tag: {} count: {:?}", tag_id, tag_count);
            continue;
        }

        context
            .db_client
            .tag()
            .upsert(
                tag::id::equals(tag_id.clone()),
                (tag_id, "".to_string(), tag_count, vec![]),
                vec![tag::count::set(tag_count)],
            )
            .exec()
            .await?;
    }
    Ok(())
}
