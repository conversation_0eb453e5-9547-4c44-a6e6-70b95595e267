use crate::Context;
use prisma_client_rust::raw;
use std::error::Error;

pub async fn execute(context: &Context) -> Result<(), Box<dyn Error>> {
    // delete read history older than 1 week
    let count = context
        .db_client
        ._execute_raw(raw!(
            "delete from \"UserHistory\" where \"viewedAt\" < now() - interval '1 week';"
        ))
        .exec()
        .await?;
    println!("prune history:{}", count);

    // correct the data if there is chapter loss (shouldn't happen)
    //     let count = context
    //         .db_client
    //         ._execute_raw(raw!(
    //             "UPDATE \"Video\"
    // SET tags = array_remove(tags, '杏吧') || '{\"杏吧傳媒\"}'
    // WHERE tags @> ARRAY['杏吧'];"
    //         ))
    //         .exec()
    //         .await?;
    //     println!("fixed ReadHistory chapter:{}", count);
    replace_tag(context, "杏吧", "杏吧傳媒").await?;
    replace_tag(context, "微密圈", "微密圈傳媒").await?;
    replace_tag(context, "色控", "色控傳媒").await?;
    replace_tag(context, "愛豆", "愛豆傳媒").await?;
    replace_tag(context, "麻豆", "麻豆傳媒").await?;
    replace_tag(context, "星空無限", "星空無限傳媒").await?;
    replace_tag(context, "密桃影像", "蜜桃影像傳媒").await?;
    replace_tag(context, "香蕉視頻", "香蕉視頻傳媒").await?;
    replace_tag(context, "糖心vlog", "糖心Vlog").await?;
    replace_tag(context, "蜜桃传媒", "蜜桃影像傳媒").await?;
    replace_tag(context, "eros", "愛神傳媒").await?;
    Ok(())
}

async fn replace_tag(context: &Context, from: &str, to: &str) -> Result<(), Box<dyn Error>> {
    let sql_str = format!(
        "UPDATE \"Video\"
SET tags = array_remove(tags, '{}') || '{{\"{}\"}}'
WHERE tags @> ARRAY['{}'];",
        from, to, from
    );
    println!("sql:{}", &sql_str);

    let count = context
        .db_client
        ._execute_raw(raw!(sql_str.as_str()))
        .exec()
        .await?;
    println!("replace from:{}, to:{}, count:{}", from, to, count);
    Ok(())
}
