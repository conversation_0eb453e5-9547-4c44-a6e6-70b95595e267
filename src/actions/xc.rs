use crate::actions::cover;
use crate::util::browser;
use crate::util::ffmpeg;
use crate::util::translate;
use crate::util::video_util;
use crate::Context;
use crate::Video;
use chrono::Utc;
use regex;
use scraper::Selector;
use std::error::Error;
use std::path::Path;

pub async fn fetch_video(
    context: &Context,
    video_url: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    // Fetch the video page
    let resp = browser::fetch_element(
        video_url,
        "div#video-player", // Selector for the video element
        false,
    )?;
    let html_content = resp.0;
    let video_element_html = resp.1;

    // Extract video URL and poster
    let video_info = get_video_info(html_content.as_str(), video_url).await?;

    // Extract m3u8 URL using regex
    // In xchina, the video source might be in a different format or location
    let download_url = extract_video_url(&html_content)?;
    let cover_image_url = get_cover_image(video_url, Some(&html_content))
        .await?
        .ok_or("Failed to get cover image")?;

    println!("Video URL: {}, Poster: {}", download_url, cover_image_url);

    // Save video info to database
    let video = video_util::save_video_in_db(context, video_info).await?;
    if video.published {
        println!("already published.");
        return Ok(());
    }

    // Download and process the video
    let video_id = video.id;
    let m3u8_filename = video_util::download_ts(
        context,
        video_id.as_str(),
        download_url.as_str(),
        Some(video_url),
    )
    .await?;

    let out_file = video_util::combine_ts(video_id.as_str(), m3u8_filename.as_str(), "output.mp4")?;
    let resize_resp = video_util::resize_video(out_file.as_str(), "output_w.mp4", rotate)?;
    let resized_file = resize_resp.0;
    let resolution = resize_resp.1;

    let video_source =
        video_util::save_video_source_in_db(context, video_id.as_str(), resolution as i32).await?;
    let duration = ffmpeg::get_video_duration(&resized_file)?;

    // Try to get a better cover image if possible
    let image_url = if let Some(vid) = video.vid {
        cover::search_get_cover_image(&vid)
            .await?
            .unwrap_or(cover_image_url.to_string())
    } else {
        cover_image_url.to_string()
    };

    let extension = if image_url.ends_with(".webp") {
        "webp"
    } else {
        "jpg"
    };

    // Download the cover image
    video_util::save_tmp_file_from_url(
        context,
        &video_id,
        &image_url,
        Some(video_url),
        true,
        false,
        &format!("cover.{}", extension),
        context.config.http_config.timeout_short,
        false,
    )
    .await?;

    // Convert webp to jpg if needed
    if extension == "webp" {
        let out_file_dir = Path::new(&out_file).parent().unwrap();
        let cover_image = image::open(out_file_dir.join("cover.webp"))?;
        let cover_image = cover_image.to_rgb8();
        cover_image.save(out_file_dir.join("cover.jpg"))?;
    }

    // Process video files
    video_util::split_ts(&resized_file, "ts")?;
    video_util::gen_thumbnail(&resized_file, "thumbs", duration)?;

    // Upload to S3
    video_util::upload_s3(
        context,
        video_id.as_str(),
        video_source.folder.as_str(),
        resolution,
        true,
    )
    .await?;

    // Publish the video
    video_util::publish_video(context, &video_id, duration).await?;
    Ok(())
}

async fn get_video_info(html_content: &str, reference: &str) -> Result<Video, Box<dyn Error>> {
    let document = scraper::Html::parse_document(html_content);

    // Extract title from meta tags (more reliable than trying to find it in the page structure)
    let title_selector = Selector::parse("meta[property='og:title']")?;
    let name = document
        .select(&title_selector)
        .next()
        .and_then(|el| el.value().attr("content"))
        .ok_or("Could not extract video title")?;

    // Clean up the title - remove site name and other unnecessary parts
    let name = name.split(" - ").next().unwrap_or(name).trim().to_string();
    let name = format_name(&name).await?;

    // Extract tags
    // In the sample HTML, tags might be in different locations, so we need to adapt
    let tags_selector = Selector::parse(".tags a div")?;
    let video_tags = document
        .select(&tags_selector)
        .map(|e| e.text().collect::<String>())
        .collect::<Vec<String>>();

    let created_at = Utc::now();

    let video = Video {
        name: name.to_string(),
        description: None,
        cover_image_url: None,
        reference: String::from(reference),
        tags: video_tags,
        vid: None,
        created_at: created_at,
        view_count: 29,
    };
    dbg!(&video);
    Ok(video)
}

fn extract_video_url(html_content: &str) -> Result<String, Box<dyn Error>> {
    // Based on the HTML sample, we need to find the video source URL
    // This might be in a script tag or as a data attribute on the video element

    // Try to find m3u8 URL using regex
    let re = regex::Regex::new(r"src: '([^']+\.m3u8)'")?;
    if let Some(caps) = re.captures(html_content) {
        if let Some(url_match) = caps.get(1) {
            return Ok(url_match.as_str().to_string());
        }
    }

    // If not found, try another approach - look for video source elements
    let document = scraper::Html::parse_document(html_content);
    let source_selector = Selector::parse("video.video-js source[type='application/x-mpegURL']")?;

    if let Some(source_element) = document.select(&source_selector).next() {
        if let Some(src) = source_element.value().attr("src") {
            return Ok(src.to_string());
        }
    }

    Err("Could not extract video URL".into())
}

pub async fn get_cover_image(
    video_url: &str,
    prefetched_data: Option<&str>,
) -> Result<Option<String>, Box<dyn Error>> {
    let html_content = if let Some(data) = prefetched_data {
        data.to_string()
    } else {
        browser::fetch_element(video_url, "body", false)?.0
    };

    // Try to extract from meta tags first (most reliable)
    let document = scraper::Html::parse_document(&html_content);
    let meta_selector = Selector::parse("meta[property='og:image']")?;

    if let Some(meta_element) = document.select(&meta_selector).next() {
        if let Some(content) = meta_element.value().attr("content") {
            return Ok(Some(content.to_string()));
        }
    }

    // If not found in meta tags, try to find it in the video poster attribute
    let video_selector = Selector::parse("video.video-js")?;
    if let Some(video_element) = document.select(&video_selector).next() {
        if let Some(poster) = video_element.value().attr("poster") {
            return Ok(Some(poster.to_string()));
        }
    }

    // If still not found, try regex approach as a fallback
    let re = regex::Regex::new(r"poster: '([^']+)'")?;
    if let Some(caps) = re.captures(&html_content) {
        if let Some(poster_match) = caps.get(1) {
            return Ok(Some(poster_match.as_str().to_string()));
        }
    }

    Ok(None)
}

async fn format_name(title: &str) -> Result<String, Box<dyn Error>> {
    // Convert Simplified Chinese to Traditional Chinese using Google Translate
    let result = translate::to_traditional(title).await?;

    // Trim any extra whitespace and return
    Ok(result.trim().to_string())
}
