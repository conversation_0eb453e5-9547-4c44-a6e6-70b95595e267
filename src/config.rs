use std::env;
use crate::{AppConfig, MSConfig, S3Config, HttpConfig};

pub fn load_config() -> AppConfig {
    // meilisearch config
    let ms_url = env::var("MEILI_URL").unwrap();
    let ms_api_key = env::var("MEILI_API_KEY").unwrap();
    let ms_index = env::var("MEILI_INDEX").unwrap();
    let ms_config = MSConfig {
        url: ms_url,
        api_key: ms_api_key,
        index: ms_index,
    };

    // s3 config
    let s3_url = env::var("S3_ENDPOINT").unwrap();
    let s3_access_key = env::var("S3_ACCESS_KEY").unwrap();
    let s3_secret_key = env::var("S3_SECRET_KEY").unwrap();
    let s3_bucket = env::var("S3_BUCKET").unwrap();
    let s3_region = env::var("S3_REGION").unwrap();
    let s3_ssl = match env::var("S3_SSL").unwrap().as_str() {
        "true" => true,
        _ => false,
    };
    let s3_config = S3Config {
        url: s3_url,
        access_key: s3_access_key,
        secret_key: s3_secret_key,
        bucket: s3_bucket,
        region: s3_region,
        ssl: s3_ssl,
    };

    let user_agent = env::var("USER_AGENT").unwrap();

    let http_connect_timeout = env::var("HTTP_CONNECT_TIMEOUT").unwrap();
    let http_timeout_short = env::var("HTTP_TIMEOUT_SHORT").unwrap();
    let http_timeout_long = env::var("HTTP_TIMEOUT_LONG").unwrap();
    let http_config = HttpConfig {
        connect_timeout: http_connect_timeout.parse().unwrap(),
        timeout_short: http_timeout_short.parse().unwrap(),
        timeout_long: http_timeout_long.parse().unwrap(),
    };

    AppConfig {
        user_agent,
        ms_config,
        s3_config,
        http_config,
    }
}
