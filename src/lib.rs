pub mod actions;
pub mod prisma;
pub mod util;
pub mod config;
use std::fmt;

use chrono::{DateTime, Utc};
use prisma::PrismaClient;
use serde::Deserialize;
use std::error::Error;
use std::sync::Arc;
use util::meilisearch::MSClient;
use util::s3::S3Client;

pub struct Context {
    pub db_client: Arc<PrismaClient>,
    pub ms_client: MSClient,
    pub s3_client: S3Client,
    pub config: AppConfig,
}

impl Context {
    pub fn new(
        db_client: PrismaClient,
        ms_client: MSClient,
        s3_client: S3Client,
        config: AppConfig,
    ) -> Self {
        Self {
            db_client: Arc::new(db_client),
            ms_client,
            s3_client,
            config,
        }
    }
}

#[derive(Debug, Deserialize, Clone)]
pub struct Video {
    name: String,
    description: Option<String>,
    cover_image_url: Option<String>,
    reference: String,
    tags: Vec<String>,
    vid: Option<String>,
    created_at: DateTime<Utc>,
    view_count: i32,
}

#[derive(Debug)]
struct FetchVideoError(String);
impl fmt::Display for FetchVideoError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl Error for FetchVideoError {}

#[derive(Debug, Clone)]
pub struct AppConfig {
    pub user_agent: String,
    pub ms_config: MSConfig,
    pub s3_config: S3Config,
    pub http_config: HttpConfig,
}

#[derive(Debug, Clone)]
pub struct MSConfig {
    pub url: String,
    pub api_key: String,
    pub index: String,
}

#[derive(Debug, Clone)]
pub struct S3Config {
    pub url: String,
    pub access_key: String,
    pub secret_key: String,
    pub bucket: String,
    pub region: String,
    pub ssl: bool,
}

#[derive(Debug, Clone)]
pub struct HttpConfig {
    /// Timeout for establishing connection in seconds
    pub connect_timeout: u64,
    /// Short timeout for quick operations in seconds
    pub timeout_short: u64,
    /// Long timeout for operations that may take longer in seconds
    pub timeout_long: u64,
}
