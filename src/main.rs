use clap::{Parser, Subcommand};
use image::GenericImageView;
use rouv_scrape::actions::{
    avple, cover, delete, local, madou, mis, pinse, playav, ppp, shise, sync_ind, sync_like,
    sync_tag, trim_db, xc,
};
use rouv_scrape::util::video_util;
use rouv_scrape::Context;
use rouv_scrape::{
    util::meilisearch::MSClient, util::s3::S3Client, AppConfig, HttpConfig, MSConfig, S3Config,
};
mod config;
use std::env;
use std::path::PathBuf;
use tracing::{info, Level};
use tracing_subscriber::FmtSubscriber;

// Stops the client from outputting a huge number of warnings during compilation.
#[allow(warnings, unused)]
use rouv_scrape::prisma::PrismaClient;

#[derive(Parser)]
#[command(version, about, long_about = None)]
struct Cli {
    /// Optional name to operate on
    name: Option<String>,

    /// Sets a custom config file
    #[arg(short, long, value_name = "FILE")]
    config: Option<PathBuf>,

    /// Turn debugging information on
    #[arg(short, long, action = clap::ArgAction::Count)]
    debug: u8,

    #[command(subcommand)]
    command: Option<Commands>,
}

#[derive(Subcommand)]
enum Commands {
    SyncInd {},
    Ppp {
        #[arg(long)]
        url: Option<String>,

        #[arg(long, default_value_t = -1)]
        rotate: i8,
    },
    Shise {
        #[arg(long)]
        url: Option<String>,

        #[arg(long, default_value_t = -1)]
        rotate: i8,
    },
    Xc {
        #[arg(long)]
        url: Option<String>,

        #[arg(long, default_value_t = -1)]
        rotate: i8,
    },
    Ap {
        #[arg(long)]
        url: Option<String>,

        #[arg(long, default_value_t = -1)]
        rotate: i8,
    },
    Mis {
        #[arg(long)]
        url: Option<String>,

        #[arg(long, default_value_t = false)]
        all: bool,

        #[arg(long, default_value_t = 1)]
        start: usize,

        #[arg(long, default_value_t = 3)]
        end: usize,
    },
    Local {
        #[arg(long)]
        url: String,

        #[arg(long, default_value_t = -1)]
        rotate: i8,
    },
    Pinse {
        #[arg(long)]
        url: Option<String>,

        #[arg(long, default_value_t = false)]
        all: bool,

        #[arg(long, default_value_t = 1)]
        start: usize,

        #[arg(long, default_value_t = 3)]
        end: usize,

        #[arg(long, default_value_t = -1)]
        rotate: i8,
    },
    Cp {
        #[arg(long)]
        url: String,

        #[arg(short, long)]
        video_id: String,
    },
    TrimDb {},
    SyncTag {},
    SyncLike {},
    Delete {},
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();
    dbg!(&args);
    let cli = Cli::parse();

    // Initialize tracing
    FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .with_target(true)
        .with_level(false)
        .with_ansi(false)
        .with_file(false)
        .with_line_number(false)
        .init();

    dotenvy::dotenv()?;

    let app_config = config::load_config();

    let db_client = PrismaClient::_builder().build().await?;
    let ms_client = MSClient::new(app_config.ms_config.clone());
    let s3_client = S3Client::new(app_config.s3_config.clone());

    let context = Context::new(db_client, ms_client, s3_client, app_config);

    match &cli.command {
        Some(Commands::SyncInd {}) => {
            sync_ind::execute(&context).await?;
        }
        Some(Commands::Ppp { url, rotate }) => {
            if let Some(video_url) = url {
                ppp::fetch_video(&context, video_url, *rotate).await?;
            }
        }
        Some(Commands::Ap { url, rotate }) => {
            if let Some(video_url) = url {
                avple::fetch_video(&context, video_url, *rotate).await?;
            }
        }
        Some(Commands::Shise { url, rotate }) => {
            if let Some(video_url) = url {
                shise::fetch_video(&context, video_url, *rotate).await?;
            }
        }
        Some(Commands::Xc { url, rotate }) => {
            if let Some(video_url) = url {
                xc::fetch_video(&context, video_url, *rotate).await?;
            }
        }
        Some(Commands::Pinse {
            url,
            all,
            start,
            end,
            rotate,
        }) => {
            let url = match url {
                Some(url) => url,
                None => "https://91pinse.com/rank/current-hot",
            };
            if *all {
                pinse::fetch_chn(&context, url, *start, *end).await?;
            } else {
                pinse::fetch_video(&context, url, *rotate).await?;
            }
        }
        Some(Commands::Mis {
            url,
            all,
            start,
            end,
        }) => {
            let url = match url {
                Some(url) => url,
                None => "https://missav.ws/dm265/chinese-subtitle",
            };
            if *all {
                mis::fetch_chn(&context, url, *start, *end).await?;
            } else {
                mis::fetch_video(&context, url, -1).await?;
            }
        }
        Some(Commands::Local { url, rotate }) => {
            local::fetch_video(&context, url, *rotate).await?;
        }
        Some(Commands::TrimDb {}) => {
            trim_db::execute(&context).await?;
        }
        Some(Commands::SyncTag {}) => {
            sync_tag::execute(&context).await?;
        }
        Some(Commands::SyncLike {}) => {
            sync_like::execute(&context).await?;
        }
        Some(Commands::Delete {}) => {
            delete::execute(&context).await?;
        }
        Some(Commands::Cp { url, video_id }) => {
            let image_url = cover::search_get_cover_image(&url).await?;

            if let Some(cover_image_url) = image_url {
                println!("cover image: {}", cover_image_url);
                let extension = if cover_image_url.ends_with("webp") {
                    "webp"
                } else {
                    "jpg"
                };
                let tmp_path = video_util::save_tmp_file_from_url(
                    &context,
                    &video_id,
                    &cover_image_url,
                    Some(&url),
                    true,
                    true,
                    &format!("cover.{}", extension),
                    context.config.http_config.timeout_short,
                    false,
                )
                .await?;

                // Convert webp to jpg if needed
                let cover_path = if extension == "webp" {
                    let tmp_pathbuf = PathBuf::from(&tmp_path);
                    let jpg_path = tmp_pathbuf.with_extension("jpg");
                    let cover_image = image::open(&tmp_path)?;
                    let cover_image = cover_image.to_rgb8();
                    cover_image.save(&jpg_path)?;
                    jpg_path.to_string_lossy().to_string()
                } else {
                    tmp_path
                };

                // Validate the final jpg image
                let cover_image =
                    image::open(&cover_path).map_err(|_| "Invalid or corrupted cover image")?;
                let (width, height) = cover_image.dimensions();
                if width < 100 || height < 100 {
                    return Err("Cover image dimensions too small".into());
                }

                video_util::upload_cover_image_s3(&context, &video_id, true, true).await?;
            }
        }
        None => {
            println!("no command");
        }
    }
    // let cmd = &args[1];
    // let rotate: i8 = if args.contains(&String::from("r")) {
    //     1
    // } else if args.contains(&String::from("rr")) {
    //     2
    // } else {
    //     -1
    // };

    // match cmd.as_str() {
    //     "cavv" => {
    //         //fetch single video fo https://cableav.tv/
    //         let video_url = &args[2];
    //         cav::fetch_video(&context, video_url.as_str(), rotate).await?;
    //     }
    //     "locv" => {
    //         //upload video from local folder
    //         let video_folder = &args[2];
    //         local::fetch_video(&context, video_folder, rotate).await?;
    //     }
    //     "madou" => {
    //         //fetch single video from madou.club
    //         madou::search_get_cover_image("pme201")?;
    //     }
    //     "avp" => {
    //         //fetch single video from https://avple.tv/
    //         let video_url = &args[2];
    //         avple::fetch_video(&context, video_url.as_str(), rotate).await?;
    //     }
    //     "pavv" => {
    //         //fetch single video from https://avple.tv/
    //         let video_url = &args[2];
    //         playav::fetch_video(&context, video_url.as_str(), rotate).await?;
    //     }
    //     "ppp" => {
    //         //fetch single video from https://ppp.porn/
    //         let video_url = &args[2];
    //         ppp::fetch_video(&context, video_url.as_str(), rotate).await?;
    //     }
    //     "mis" => {
    //         //fetch single video from https://ppp.porn/
    //         let video_url = &args[2];
    //         mis::fetch_video(&context, video_url.as_str(), rotate).await?;
    //     }
    //     "cp" => {
    //         // copy cover image to video
    //         let source_url = &args[2];
    //         let video_id = &args[3];
    //         let image_url = cover::search_get_cover_image(&source_url).await?;

    //         if let Some(cover_image_url) = image_url {
    //             println!("cover image: {}", cover_image_url);
    //             video_util::save_tmp_file_from_url(
    //                 &context,
    //                 &video_id,
    //                 &cover_image_url,
    //                 false,
    //                 true,
    //                 "cover.jpg",
    //             )
    //             .await?;
    //             video_util::upload_cover_image_s3(&context, &video_id, false, true).await?;
    //         }
    //     }
    //     "sync-ind" => {
    //         sync_ind::sync_index(&context).await?;
    //     }
    //     _ => {
    //         println!("unknown command")
    //     }
    // }

    Ok(())
}
