use headless_chrome::protocol::cdp::Runtime;
use headless_chrome::Browser;
use headless_chrome::LaunchOptionsBuilder;
use serde::de::DeserializeOwned;
use std::error::Error;
use std::ffi::OsStr;
use std::fs::File;
use std::io::Write;

pub fn fetch(url: &str, headless: bool) -> Result<String, Box<dyn Error>> {
    // let browser = Browser::default()?;
    let browser = Browser::new(
        LaunchOptionsBuilder::default()
            .args(vec![
                OsStr::new("--no-sandbox"),
                OsStr::new("--disable-dev-shm-usage"),
            ])
            .headless(headless)
            .build()
            .unwrap(),
    )?;

    let tab = browser.new_tab()?;

    tab.navigate_to(url)?;

    tab.wait_until_navigated()?;
    tab.wait_for_element("body")?;

    // let script = tab.wait_for_element("#__next > div > div:nth-child(4) > div.MuiBox-root.jss23.jss17 > div > script:nth-child(5)")?;
    // dbg!(script.get_content());

    let content = tab.get_content()?;

    // let mut file = File::create("tmp.html")?;
    // file.write_all(content.as_bytes())?;
    Ok(content)
}

pub fn fetch_element(
    url: &str,
    element: &str,
    headless: bool,
) -> Result<(String, String), Box<dyn Error>> {
    let browser = Browser::new(
        LaunchOptionsBuilder::default()
            .args(vec![
                OsStr::new("--no-sandbox"),
                OsStr::new("--disable-dev-shm-usage"),
            ])
            .headless(headless)
            .build()
            .unwrap(),
    )?;

    let tab = browser.new_tab()?;
    tab.navigate_to(url)?;

    // tab.wait_for_element("video#player");

    let ele = tab.wait_for_element(element)?;

    let page_content = tab.get_content()?;
    let content = ele.get_content()?;

    let mut file = File::create("tmp.html")?;
    file.write_all(page_content.as_bytes())?;
    Ok((page_content, content))
}

pub async fn fetch_element_attr(
    url: &str,
    element: &str,
    attr: &str,
    headless: bool,
) -> Result<(String, String), Box<dyn Error>> {
    let browser = Browser::new(
        LaunchOptionsBuilder::default()
            .args(vec![
                OsStr::new("--no-sandbox"),
                OsStr::new("--disable-dev-shm-usage"),
            ])
            .headless(headless)
            .build()
            .unwrap(),
    )?;

    let tab = browser.new_tab()?;
    tab.navigate_to(url)?;

    // tab.wait_for_element("video#player");

    let ele = tab.wait_for_element(element)?;

    // Wait until the attribute is not present using a loop
    loop {
        // Evaluate JavaScript to check if the attribute exists
        let script_content = format!(
            "document.querySelector('{}').hasAttribute('{}')",
            element, attr
        );
        let result = tab
            .evaluate(&script_content, true)
            .expect("Failed to evaluate script.");

        // Extract the result as a boolean
        let attribute_exists = result
            .value
            .unwrap()
            .as_bool()
            .expect("Failed to extract boolean from evaluation result.");

        if attribute_exists {
            // println!("Attribute '{}' is not present.", attr);
            break;
        }

        // Wait for a short duration before checking again
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
    }
    let page_content = tab.get_content()?;
    let content = ele.get_content()?;

    // let mut file = File::create("tmp.html")?;
    // file.write_all(content.as_bytes());
    Ok((page_content, content))
}

pub fn fetch_jsobject<T>(url: &str, js_element: &str, headless: bool) -> Result<T, Box<dyn Error>>
where
    T: DeserializeOwned,
{
    let browser = Browser::new(
        LaunchOptionsBuilder::default()
            .args(vec![
                OsStr::new("--no-sandbox"),
                OsStr::new("--disable-dev-shm-usage"),
            ])
            .headless(headless)
            .build()
            .unwrap(),
    )?;

    let tab = browser.new_tab()?;
    tab.navigate_to(url)?;

    // tab.wait_for_element("video#player");

    let ele = tab.wait_for_element(js_element)?;
    let json_data = ele.get_inner_text()?;
    // println!("script:{}", json_data);
    let data: T = serde_json::from_str(json_data.as_str()).expect("Failed to deserialize JSON");
    Ok(data)
}

pub fn fetch_js_and_eval<T>(
    url: &str,
    js_element: &str,
    ret_obj: &str,
    headless: bool,
) -> Result<(String, T), Box<dyn Error>>
where
    T: DeserializeOwned,
{
    let browser = Browser::new(
        LaunchOptionsBuilder::default()
            .args(vec![
                OsStr::new("--no-sandbox"),
                OsStr::new("--disable-dev-shm-usage"),
            ])
            .headless(headless)
            .build()
            .unwrap(),
    )?;

    let tab = browser.new_tab()?;
    tab.navigate_to(url)?;

    let ele = tab.wait_for_element(js_element)?;
    let html_element = tab.wait_for_element("html")?;
    let page_content = html_element.get_content()?;
    // println!("page content: {}", page_content);
    let script = ele.get_inner_text()?;

    let js_func = format!("const f=() => {{{}\nreturn {};}}; f();", script, ret_obj);
    let obj = tab
        .call_method(Runtime::Evaluate {
            expression: js_func,
            return_by_value: Some(true),
            generate_preview: Some(false),
            silent: Some(false),
            await_promise: Some(false),
            include_command_line_api: Some(false),
            user_gesture: Some(false),
            object_group: None,
            context_id: None,
            throw_on_side_effect: None,
            timeout: None,
            disable_breaks: None,
            repl_mode: None,
            allow_unsafe_eval_blocked_by_csp: None,
            unique_context_id: None,
        })?
        .result
        .value
        .ok_or("can not get js object")?;

    let data: T = serde_json::from_value(obj).expect("Failed to deserialize JSON");
    Ok((page_content, data))
}

pub fn fetch_js_and_eval_str(
    url: &str,
    js_element: &str,
    ret_obj: &str,
    headless: bool,
) -> Result<(String, String), Box<dyn Error>> {
    let browser = Browser::new(
        LaunchOptionsBuilder::default()
            .args(vec![
                OsStr::new("--no-sandbox"),
                OsStr::new("--disable-dev-shm-usage"),
            ])
            .headless(headless)
            .build()
            .unwrap(),
    )?;

    let tab = browser.new_tab()?;
    tab.navigate_to(url)?;

    let ele = tab.wait_for_element(js_element)?;
    let html_element = tab.wait_for_element("html")?;
    let page_content = html_element.get_content()?;
    // println!("page content: {}", page_content);
    let script = ele.get_inner_text()?;

    let js_func = format!("const f=() => {{{}\nreturn {};}}; f();", script, ret_obj);
    let obj = tab
        .call_method(Runtime::Evaluate {
            expression: js_func,
            return_by_value: Some(true),
            generate_preview: Some(false),
            silent: Some(false),
            await_promise: Some(false),
            include_command_line_api: Some(false),
            user_gesture: Some(false),
            object_group: None,
            context_id: None,
            throw_on_side_effect: None,
            timeout: None,
            disable_breaks: None,
            repl_mode: None,
            allow_unsafe_eval_blocked_by_csp: None,
            unique_context_id: None,
        })?
        .result
        .value
        .ok_or("can not get js object")?;

    dbg!(&obj);
    let data = obj.as_str().unwrap().to_string();
    Ok((page_content, data))
}

pub fn get_user_agent() -> Result<Option<String>, Box<dyn Error>> {
    let browser = Browser::new(
        LaunchOptionsBuilder::default()
            .headless(false)
            .build()
            .unwrap(),
    )?;

    let tab = browser.new_tab()?;
    // Enable the Runtime domain to execute JavaScript
    tab.enable_runtime()
        .expect("Failed to enable Runtime domain");

    // Evaluate JavaScript to get the User-Agent
    let expression = "navigator.userAgent";
    let result = tab
        .evaluate(expression, true)
        .expect("Failed to evaluate JavaScript");

    // dbg!(result);

    // Extract the User-Agent from the result
    if let Some(value) = result.value {
        if let Some(user_agent) = value.as_str() {
            return Ok(Some(String::from(user_agent)));
        }
    }

    Ok(None)
}
