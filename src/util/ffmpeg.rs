use std::env;
use std::error::Error;
use std::fmt;
use std::fs;
use std::path::Path;
use std::process::Command;
use std::str::from_utf8;

const FFMPEG: &str = "/opt/homebrew/bin/ffmpeg";
const FFPROBE: &str = "/opt/homebrew/bin/ffprobe";
// const FFMPEG: &str = "D:\\Users\\axu\\local\\ffmpeg-6.1.1-full_build\\bin\\ffmpeg";
// const FFPROBE: &str = "D:\\Users\\axu\\local\\ffmpeg-6.1.1-full_build\\bin\\ffprobe";

#[derive(Debug)]
struct FFMPEGError(String);

impl fmt::Display for FFMPEGError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl Error for FFMPEGError {}

pub fn combine_ts(playlist_file: &str, output_file: &str) -> Result<(), Box<dyn Error>> {
    let use_gpu = env::var("USE_GPU")?;
    let mut binding = Command::new(FFMPEG);
    let mut output = binding
        .arg("-y")
        .arg("-allowed_extensions")
        .arg("ALL")
        .arg("-i")
        .arg(playlist_file)
        .arg("-c")
        .arg("copy")
        .arg(output_file);
    if use_gpu.as_str() == "true" {
        output = output.arg("-hwaccel").arg("cuda");
    }
    // Log command
    let cmdline = format!(
        "{} {}",
        output.get_program().to_string_lossy(),
        output.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffmpeg] Running command: {}", cmdline);
    let output = output.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    Ok(())
}

pub fn get_resolution(file_name: &str) -> Result<(usize, usize), Box<dyn Error>> {
    let mut cmd = Command::new(FFPROBE);
    cmd.arg("-v")
        .arg("error")
        .arg("-select_streams")
        .arg("v:0")
        .arg("-show_entries")
        .arg("stream=width,height")
        .arg("-of")
        .arg("csv=s=x:p=0")
        .arg(file_name);
    let cmdline = format!(
        "{} {}",
        cmd.get_program().to_string_lossy(),
        cmd.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffprobe] Running command: {}", cmdline);
    let output = cmd.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    let resolution = from_utf8(&output.stdout)?.trim();
    let segs: Vec<&str> = resolution.split('x').collect();

    let width: usize = segs[0].parse()?;
    let height: usize = segs[1].parse()?;

    Ok((width, height))
}

pub fn get_video_duration(file_name: &str) -> Result<f64, Box<dyn Error>> {
    let mut cmd = Command::new(FFPROBE);
    cmd.arg("-i")
        .arg(file_name)
        .arg("-show_format")
        .arg("-v")
        .arg("quiet");
    let cmdline = format!(
        "{} {}",
        cmd.get_program().to_string_lossy(),
        cmd.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffprobe] Running command: {}", cmdline);
    let output = cmd.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    let output_str = from_utf8(&output.stdout)?.trim();
    for line in output_str.lines() {
        if let Some(index) = line.find('=') {
            let segs: Vec<&str> = line.split('=').collect();
            if segs[0] == "duration" {
                let duration = segs[1].trim().parse()?;
                return Ok(duration);
            }
        }
    }

    Err(Box::new(FFMPEGError("Cannot get duration".to_string())))
}

pub fn resize_and_add_watermark(
    input_file: &str,
    watermark_file: &str,
    target_height: usize,
    output_file: &str,
    rotate: i8,
) -> Result<(), Box<dyn Error>> {
    let use_gpu = env::var("USE_GPU")?;
    let mut binding = Command::new(FFMPEG);

    let filter = if rotate >= 0 {
        //1 = 90° clockwise
        //2 = 90° counterclockwise
        format!(
            "transpose={},overlay=x=(main_w-overlay_w)-8:y=(main_h-overlay_h)-8,scale=-2:{}",
            rotate, target_height
        )
    } else {
        format!(
            "overlay=x=(main_w-overlay_w)-8:y=(main_h-overlay_h)-8,scale=-2:{}",
            target_height
        )
        // format!("scale=-2:{}", target_height)
    };

    let mut output = binding
        .arg("-y")
        .arg("-i")
        .arg(input_file)
        .arg("-i")
        .arg(watermark_file)
        .arg("-filter_complex")
        .arg(filter)
        .arg(output_file);
    if use_gpu.as_str() == "true" {
        println!("going to use GPU");
        output = output.arg("-hwaccel").arg("cuda");
    }
    let cmdline = format!(
        "{} {}",
        output.get_program().to_string_lossy(),
        output.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffmpeg] Running command: {}", cmdline);
    let output = output.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    Ok(())
}

pub fn resize(
    input_file: &str,
    target_height: usize,
    output_file: &str,
) -> Result<(), Box<dyn Error>> {
    let use_gpu = env::var("USE_GPU")?;
    let mut binding = Command::new(FFMPEG);
    let mut output = binding
        .arg("-y")
        .arg("-i")
        .arg(input_file)
        .arg("-filter_complex")
        .arg(format!("scale=-2:{}", target_height))
        .arg(output_file);
    if use_gpu.as_str() == "true" {
        println!("going to use GPU");
        output = output.arg("-hwaccel").arg("cuda");
    }
    let cmdline = format!(
        "{} {}",
        output.get_program().to_string_lossy(),
        output.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffmpeg] Running command: {}", cmdline);
    let output = output.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    Ok(())
}

pub fn gen_thumbnail(
    file_name: &str,
    out_dir: &str,
    sec_per_thumb: usize,
) -> Result<(), Box<dyn Error>> {
    // Create outDir if not exist
    if let Err(err) = fs::create_dir_all(out_dir) {
        if err.kind() != std::io::ErrorKind::AlreadyExists {
            return Err(Box::new(FFMPEGError(format!(
                "Failed to create directory: {}",
                err
            ))));
        }
    }

    let mut cmd = Command::new(FFMPEG);
    cmd.arg("-y")
        .arg("-i")
        .arg(file_name)
        .arg("-vf")
        .arg(format!("fps=1/{}", sec_per_thumb))
        .arg(format!("{}/tmp-%04d.jpg", out_dir));
    let cmdline = format!(
        "{} {}",
        cmd.get_program().to_string_lossy(),
        cmd.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffmpeg] Running command: {}", cmdline);
    let output = cmd.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    Ok(())
}

pub fn split_to_ts(file_name: &str, out_dir: &str, hls_time: &str) -> Result<(), Box<dyn Error>> {
    // Create outDir if not exist
    if let Err(err) = fs::create_dir_all(out_dir) {
        if err.kind() != std::io::ErrorKind::AlreadyExists {
            return Err(Box::new(FFMPEGError(format!(
                "Failed to create directory: {}",
                err
            ))));
        }
    }

    let mut cmd = Command::new(FFMPEG);
    cmd.arg("-i")
        .arg(file_name)
        .arg("-bsf:v")
        .arg("h264_mp4toannexb")
        .arg("-codec")
        .arg("copy")
        .arg("-hls_list_size")
        .arg("0")
        .arg("-hls_time")
        .arg(hls_time)
        .arg("-hls_segment_filename")
        .arg(format!("{}/CLS-%03d.ts", out_dir))
        .arg(format!("{}/index.m3u8", out_dir));
    let cmdline = format!(
        "{} {}",
        cmd.get_program().to_string_lossy(),
        cmd.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffmpeg] Running command: {}", cmdline);
    let output = cmd.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    Ok(())
}

pub fn screenshot(file_name: &str, second: usize, out_file: &str) -> Result<(), Box<dyn Error>> {
    let mut cmd = Command::new(FFMPEG);
    cmd.arg("-ss")
        .arg(second.to_string())
        .arg("-i")
        .arg(file_name)
        .arg("-vframes")
        .arg("1")
        .arg(out_file);
    let cmdline = format!(
        "{} {}",
        cmd.get_program().to_string_lossy(),
        cmd.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffmpeg] Running command: {}", cmdline);
    let output = cmd.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }

    Ok(())
}

// take 2 screenshots and combine them for portrait video
pub fn screenshot2(file_name: &str, duration: f64, out_file: &str) -> Result<(), Box<dyn Error>> {
    let image_path1 = Path::new(file_name)
        .parent()
        .unwrap()
        .join("tmp_cover1.jpg");
    let image_file1 = image_path1.to_str().unwrap();
    let image_path2 = Path::new(file_name)
        .parent()
        .unwrap()
        .join("tmp_cover2.jpg");
    let image_file2 = image_path2.to_str().unwrap();
    screenshot(file_name, 2, image_file1)?;
    let ss = (duration / 2 as f64) as usize;
    screenshot(file_name, ss, image_file2)?;

    // combine two screenshots
    let mut cmd = Command::new(FFMPEG);
    cmd.arg("-i")
        .arg(image_file1)
        .arg("-i")
        .arg(image_file2)
        .arg("-filter_complex")
        .arg("hstack=inputs=2")
        .arg(out_file);
    let cmdline = format!(
        "{} {}",
        cmd.get_program().to_string_lossy(),
        cmd.get_args().map(|a| a.to_string_lossy()).collect::<Vec<_>>().join(" ")
    );
    println!("[ffmpeg] Running command: {}", cmdline);
    let output = cmd.output()?;

    if !output.status.success() {
        let stderr = from_utf8(&output.stderr).unwrap_or("");
        let stdout = from_utf8(&output.stdout).unwrap_or("");
        let error_message = format!(
            "Command failed: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
            output.status, stdout, stderr
        );
        return Err(Box::new(FFMPEGError(error_message)));
    }
    Ok(())
}
