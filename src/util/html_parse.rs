use scraper::{Html, Selector};
use std::error::Error;

pub struct SelectorFinder {
    parent_selector: Selector,
    parent_selector_str: String,
}

impl SelectorFinder {
    pub fn new(parent_selector: &str) -> Result<Self, Box<dyn Error>> {
        Ok(SelectorFinder {
            parent_selector: Selector::parse(parent_selector)
                .map_err(|e| format!("Failed to parse selector: {}", e))?,
            parent_selector_str: parent_selector.to_string(),
        })
    }

    pub fn find_elements_with_text(
        &self,
        html: &str,
        search_text: &str,
    ) -> Result<Vec<String>, Box<dyn Error>> {
        let document = Html::parse_document(html);
        let mut found_selectors = Vec::new();

        for parent_elem in document.select(&self.parent_selector) {
            fn traverse_element(
                element: scraper::ElementRef,
                search_text: &str,
                selectors: &mut Vec<String>,
                parent_selector: &str,
            ) {
                if element.text().any(|t| t.contains(search_text)) {
                    let mut selector_path = String::from(parent_selector);

                    if !selector_path.is_empty() {
                        selector_path.push(' ');
                    }

                    // Build base selector for current element
                    let mut current_selector = String::new();
                    current_selector.push_str(&element.value().name.local);

                    if let Some(classes) = element.value().attr("class") {
                        for class in classes.split_whitespace() {
                            current_selector.push('.');
                            current_selector.push_str(class);
                        }
                    }

                    if let Some(id) = element.value().attr("id") {
                        current_selector.push('#');
                        current_selector.push_str(id);
                    }

                    // Find nth-child index
                    let parent = element.parent().and_then(|p| scraper::ElementRef::wrap(p));
                    if let Some(parent_ref) = parent {
                        // Get all children that match the same tag and classes
                        let element_tag = element.value().name.local.clone();
                        let element_classes =
                            element.value().attr("class").unwrap_or("").to_string();

                        let matching_siblings: Vec<_> = parent_ref
                            .children()
                            .filter_map(|child| {
                                if let Some(child_elem) = child.value().as_element() {
                                    if child_elem.name.local == element_tag
                                        && child_elem.attr("class").unwrap_or("") == element_classes
                                    {
                                        scraper::ElementRef::wrap(child)
                                    } else {
                                        None
                                    }
                                } else {
                                    None
                                }
                            })
                            .collect();

                        if matching_siblings.len() > 1 {
                            // Find position of current element
                            let pos = matching_siblings
                                .iter()
                                .position(|sibling| std::ptr::eq(sibling.value(), element.value()))
                                .map(|i| i + 1)
                                .unwrap_or(1);

                            current_selector.push_str(&format!(":nth-of-type({})", pos));
                        }
                    }

                    selector_path.push_str(&current_selector);
                    selectors.push(selector_path);
                }

                for child in element.children() {
                    if let Some(child_elem) = child.value().as_element() {
                        if let Some(child_ref) = scraper::ElementRef::wrap(child) {
                            traverse_element(child_ref, search_text, selectors, parent_selector);
                        }
                    }
                }
            }

            traverse_element(
                parent_elem,
                search_text,
                &mut found_selectors,
                &self.parent_selector_str,
            );
        }

        Ok(found_selectors)
    }
}
