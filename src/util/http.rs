use crate::Context;
use bytes::Bytes;
use http::Extensions;
use reqwest::{Request, Response};
use reqwest_middleware::{ClientBuilder, Middleware, Next};
use reqwest_retry::{
    default_on_request_failure, policies::ExponentialBackoff, RetryTransientMiddleware, Retryable,
    RetryableStrategy,
};
use std::env;
use std::error::Error;
use std::fs;
use std::io::Write; // bring trait into scope
use std::time::Duration;

// Stops the client from outputting a huge number of warnings during compilation.
#[allow(warnings, unused)]
use crate::prisma::PrismaClient;

// Log each request to show that the requests will be retried
struct LoggingMiddleware;

#[async_trait::async_trait]
impl Middleware for LoggingMiddleware {
    async fn handle(
        &self,
        req: Request,
        extensions: &mut Extensions,
        next: Next<'_>,
    ) -> reqwest_middleware::Result<Response> {
        // println!("Request started {}", req.url());
        let res = next.run(req, extensions).await;
        // println!("Request finished");
        res
    }
}

// Just a toy example, retry when the successful response code is 201, else do nothing.
struct RetryTest;
impl RetryableStrategy for RetryTest {
    fn handle(&self, res: &reqwest_middleware::Result<reqwest::Response>) -> Option<Retryable> {
        match res {
            // otherwise do not retry a successful request
            Ok(success) => {
                // println!("status:{}", success.status());
                None
            }
            // but maybe retry a request failure
            Err(error) => default_on_request_failure(error),
        }
    }
}

pub async fn fetch(
    context: &Context,
    url: &str,
    referer: Option<&str>,
    timeout: u64,
) -> Result<Bytes, Box<dyn Error>> {
    // Retry up to 3 times with increasing intervals between attempts.
    // Create the actual middleware, with the exponential backoff and custom retry strategy.
    // Exponential backoff with max 3 retries
    let retry_policy = ExponentialBackoff::builder().build_with_max_retries(3);

    let ret_s = RetryTransientMiddleware::new_with_policy_and_strategy(retry_policy, RetryTest);
    let mut client_builder = reqwest::Client::builder();

    let socks5_proxy = env::var("SOCKS5_PROXY").unwrap_or_default();
    if !socks5_proxy.is_empty() {
        // println!("proxy:{}", socks5_proxy);
        client_builder = client_builder.proxy(reqwest::Proxy::all(socks5_proxy)?);
    }
    let client = client_builder
        .connect_timeout(Duration::from_secs(
            context.config.http_config.connect_timeout,
        ))
        .build()?;

    let mut req_builder = client
        .get(url)
        .timeout(Duration::from_secs(timeout))
        .header("User-Agent", context.config.user_agent.as_str())
        .header("Connection", "keep-alive");

    if let Some(referer) = referer {
        req_builder = req_builder.header("Referer", referer);
    }

    // let resp = client
    //     .get(url)
    //     .timeout(Duration::from_secs(fetch_timeout))
    //     .header("User-Agent", context.config.user_agent.as_str())
    //     .header("Connection", "keep-alive")
    //     .send()
    //     .await?;
    let resp = req_builder.send().await?;
    let content = resp.bytes().await?;
    Ok(content)
}

pub async fn fetch_with_curl(
    context: &Context,
    url: &str,
    referer: Option<&str>,
    timeout: u64,
) -> Result<Bytes, Box<dyn Error>> {
    use tokio::process::Command;
    use std::time::Duration;
    use tokio::time::sleep;

    let max_retries = 3;
    let mut delay = Duration::from_millis(500);
    let user_agent = context.config.user_agent.as_str();
    let socks5_proxy = std::env::var("SOCKS5_PROXY").unwrap_or_default();

    for attempt in 0..max_retries {
        let mut cmd = Command::new("curl");
        cmd.arg("-L")
            .arg("--silent")
            .arg("--show-error")
            .arg("-A").arg(user_agent)
            .arg("-H").arg("Connection: keep-alive")
            .arg("--max-time").arg(timeout.to_string())
            .arg("--connect-timeout").arg(timeout.to_string());
        if let Some(referer) = referer {
            cmd.arg("-e").arg(referer);
        }
        if !socks5_proxy.is_empty() {
            cmd.arg("-x").arg(&socks5_proxy);
        }
        cmd.arg(url);

        let output = cmd.output().await;
        match output {
            Ok(output) => {
                if output.status.success() {
                    return Ok(Bytes::copy_from_slice(&output.stdout));
                } else {
                    // Optionally log stderr for debugging
                    eprintln!("curl error: {}", String::from_utf8_lossy(&output.stderr));
                }
            }
            Err(_e) => {
                // Optionally log error
                eprintln!("Failed to run curl: {}", _e);
            }
        }
        if attempt < max_retries - 1 {
            sleep(delay).await;
            delay *= 2;
        }
    }
    Err("curl fetch failed after retries".into())
}

#[cfg(test)]
mod tests {

    use super::*;
    use crate::config::load_config;
    use crate::{util::meilisearch::MSClient, util::s3::S3Client};

    #[tokio::test]
    async fn it_works() {
        dotenvy::dotenv().ok();
        let app_config = load_config();

        let db_client = PrismaClient::_builder().build().await.unwrap();
        let ms_client = MSClient::new(app_config.ms_config.clone());
        let s3_client = S3Client::new(app_config.s3_config.clone());
        let context = Context::new(db_client, ms_client, s3_client, app_config);

        let content = fetch_with_curl(
            &context,
            "https://bh.fdata.cc/videos/1102970.mp4",
            None,
            10,
        )
        .await
        .unwrap();
        // let content = fetch(&context, "http://roudl.xyz/download/app-release6.apk")
        //     .await
        //     .unwrap();

        let mut file = fs::OpenOptions::new()
            .create(true) // To create a new file
            .write(true)
            // either use the ? operator or unwrap since it returns a Result
            .open("tmp.mp4")
            .unwrap();

        file.write_all(&content);

        // assert_eq!(result, 4);
    }
}
