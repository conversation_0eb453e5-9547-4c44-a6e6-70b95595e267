use crate::MSConfig;
use chrono::{DateTime, FixedOffset};
use meilisearch_sdk::client::Client;
use serde::Serialize;
use std::error::Error;

#[derive(Debug, Serialize)]
pub struct MSVideo {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "vid")]
    pub vid: Option<String>,
    #[serde(rename = "name")]
    pub name: String,
    #[serde(rename = "nameZh")]
    pub name_zh: String,
    #[serde(rename = "duration")]
    pub duration: f64,
    #[serde(rename = "viewCount")]
    pub view_count: i32,
    #[serde(rename = "tags")]
    pub tags: Vec<String>,
    #[serde(rename = "tagsZh")]
    pub tags_zh: Vec<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<FixedOffset>,
    #[serde(rename = "sources")]
    pub sources: Vec<MSVideoSource>,
}

#[derive(Debug, Serialize)]
pub struct MSVideoSource {
    #[serde(rename = "resolution")]
    pub resolution: i32,
}

pub struct MSClient {
    client: Client,
    index: String,
}

impl MSClient {
    pub fn new(config: MSConfig) -> Self {
        let client = Client::new(config.url, Some(config.api_key)).unwrap();
        let index = config.index;
        Self { client, index }
    }

    pub async fn save_video(&self, video: MSVideo) -> Result<(), Box<dyn Error>> {
        let index = self.client.index(&self.index);
        index.add_documents(&[video], Some("id")).await?;
        Ok(())
    }

    pub async fn delete_video(&self, video_id: &str) -> Result<(), Box<dyn Error>> {
        let index = self.client.index(&self.index);
        index.delete_document(video_id).await?;
        Ok(())
    }
}
