use aws_credential_types::provider::{ProvideCredentials, SharedCredentialsProvider};
use aws_sdk_s3::{config::Region, primitives::ByteStream, types::Object, Client};
use aws_smithy_runtime::client::http::hyper_014::HyperClientBuilder;
use rustls::{ClientConfig, RootCertStore};
use std::sync::Arc;

use crate::S3Config;

pub struct NoCertificateVerification {}

impl rustls::client::ServerCertVerifier for NoCertificateVerification {
    fn verify_server_cert(
        &self,
        _end_entity: &rustls::Certificate,
        _intermediates: &[rustls::Certificate],
        _server_name: &rustls::ServerName,
        _scts: &mut dyn Iterator<Item = &[u8]>,
        _ocsp: &[u8],
        _now: std::time::SystemTime,
    ) -> Result<rustls::client::ServerCertVerified, rustls::Error> {
        Ok(rustls::client::ServerCertVerified::assertion())
    }
}

fn make_provider(s3_config: S3Config) -> impl ProvideCredentials {
    // ...
    let access_key = s3_config.access_key;
    let secret_key = s3_config.secret_key;

    let credential = aws_sdk_s3::config::Credentials::new(access_key, secret_key, None, None, "");
    return credential;
}

pub struct S3Client {
    bucket: String,
    client: Client,
}

impl S3Client {
    pub fn new(s3_config: S3Config) -> Self {
        // S3 client
        let endpoint_url = s3_config.clone().url;
        // let access_key = env::var("S3_ACCESS_KEY")?;
        // let secret_key = env::var("S3_SECRET_KEY")?;
        // let bucket = env::var("S3_BUCKET")?;

        let root_store = RootCertStore::empty();
        let mut tls_config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(root_store)
            .with_no_client_auth();

        tls_config
            .dangerous()
            .set_certificate_verifier(Arc::new(NoCertificateVerification {}));

        // Rustls connector.
        let rustls_connector = hyper_rustls::HttpsConnectorBuilder::new()
            .with_tls_config(tls_config)
            .https_only()
            .enable_http1()
            .build();

        // Hyper client builder.
        let http_client = HyperClientBuilder::new().build(rustls_connector);

        // let credential = aws_sdk_s3::config::Credentials::new(access_key, secret_key, None, None, "");
        let config = aws_sdk_s3::config::Builder::new()
            .endpoint_url(endpoint_url)
            .region(Region::new(s3_config.region.clone()))
            .credentials_provider(SharedCredentialsProvider::new(make_provider(
                s3_config.clone(),
            )))
            .force_path_style(true)
            .http_client(http_client)
            .build();
        let client = Client::from_conf(config);
        Self {
            bucket: s3_config.clone().bucket,
            client,
        }
    }

    pub async fn list_buckets(&self) -> Result<(), Box<dyn std::error::Error>> {
        // let client = get_client().await?;
        let resp = self.client.list_buckets().send().await?;
        let buckets = resp.buckets.unwrap();
        for bucket in buckets {
            println!("b: {}", bucket.name.unwrap());
        }
        Ok(())
    }

    pub async fn get_object(&self, key: &str) -> Result<ByteStream, Box<dyn std::error::Error>> {
        // let bucket = env::var("S3_BUCKET").unwrap();

        // let client = get_client().await?;
        // let resp = client.list_buckets().send().await?;
        let response = self
            .client
            .get_object()
            .bucket(self.bucket.clone())
            .key(key)
            .send()
            .await?;

        let data = response.body;
        // let buckets = resp.buckets();
        Ok(data)
    }

    pub async fn check_file_exist(&self, key: &str) -> Result<bool, Box<dyn std::error::Error>> {
        // let bucket = env::var("S3_BUCKET").unwrap();

        // let client = get_client().await?;

        if let Ok(_) = self
            .client
            .head_object()
            .bucket(self.bucket.clone())
            .key(key)
            .send()
            .await
        {
            return Ok(true);
        } else {
            return Ok(false);
        }
    }

    pub async fn copy_object(
        &self,
        source: &str,
        target: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let bucket = &self.bucket;
        // let client = get_client().await?;

        let mut source_bucket_and_object: String = "".to_owned();
        source_bucket_and_object.push_str(bucket);
        source_bucket_and_object.push('/');
        source_bucket_and_object.push_str(source);

        self.client
            .copy_object()
            .copy_source(source_bucket_and_object)
            .bucket(bucket)
            .key(target)
            .send()
            .await?;
        Ok(())
    }

    pub async fn put_object(
        &self,
        key: &str,
        body: ByteStream,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.client
            .put_object()
            .bucket(self.bucket.clone())
            .key(key)
            .body(body)
            .send()
            .await?;

        Ok(())
    }

    pub async fn delete_object(&self, key: &str) -> Result<(), Box<dyn std::error::Error>> {
        self.client
            .delete_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await?;

        Ok(())
    }

    pub async fn list_all_files(
        &self,
        dir: &str,
        recursive: bool,
    ) -> Result<Vec<Object>, Box<dyn std::error::Error>> {
        let mut files = Vec::new();
        let mut continuation_token = None;

        loop {
            let response = self
                .list_files(dir, recursive, continuation_token.as_deref())
                .await?;

            if let Some(contents) = response.contents {
                files.extend(contents);
            }

            if !response.is_truncated.unwrap_or(false) {
                break;
            }

            continuation_token = response.next_continuation_token;
        }

        Ok(files)
    }

    async fn list_files(
        &self,
        dir: &str,
        recursive: bool,
        continuation_token: Option<&str>,
    ) -> Result<
        aws_sdk_s3::operation::list_objects_v2::ListObjectsV2Output,
        Box<dyn std::error::Error>,
    > {
        let mut req = self
            .client
            .list_objects_v2()
            .bucket(&self.bucket)
            .max_keys(20)
            .prefix(dir);

        if !recursive {
            req = req.delimiter("/");
        }

        if let Some(token) = continuation_token {
            req = req.continuation_token(token);
        }

        let files = req.send().await?;
        Ok(files)
    }
}
