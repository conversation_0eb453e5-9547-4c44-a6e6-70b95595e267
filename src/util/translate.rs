use serde_json::json;
use std::error::Error;

pub async fn to_traditional(text: &str) -> Result<String, Box<dyn Error>> {
    let api_key = std::env::var("GOOGLE_TRANSLATE_API_KEY")?;
    let client = reqwest::Client::new();

    let response = client
        .post("https://translation.googleapis.com/language/translate/v2")
        .query(&[("key", api_key)])
        .json(&json!({
            "q": text,
            "source": "zh-CN",
            "target": "zh-TW",
            "format": "text"
        }))
        .send()
        .await?
        .json::<serde_json::Value>()
        .await?;

    let translated_text = response["data"]["translations"][0]["translatedText"]
        .as_str()
        .ok_or("Failed to get translated text")?;

    Ok(translated_text.to_string())
}

pub async fn to_simplified(text: &str) -> Result<String, Box<dyn Error>> {
    let api_key = std::env::var("GOOGLE_TRANSLATE_API_KEY")?;
    let client = reqwest::Client::new();

    let response = client
        .post("https://translation.googleapis.com/language/translate/v2")
        .query(&[("key", api_key)])
        .json(&json!({
            "q": text,
            "source": "zh-TW",
            "target": "zh-CN",
            "format": "text"
        }))
        .send()
        .await?
        .json::<serde_json::Value>()
        .await?;

    let translated_text = response["data"]["translations"][0]["translatedText"]
        .as_str()
        .ok_or("Failed to get translated text")?;

    Ok(translated_text.to_string())
}
