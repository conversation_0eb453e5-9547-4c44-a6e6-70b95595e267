use crate::prisma::{source, video};
use crate::util::{ffmpeg, http, s3};
use crate::{Context, FetchVideoError, Video};
use aws_sdk_s3::primitives::ByteStream;
use chrono::{FixedOffset, TimeZone, Utc};
use image::codecs::jpeg::JpegEncoder;
use image::{imageops, DynamicImage, RgbaImage};
use m3u8_rs::{MasterPlaylist, Playlist, VariantStream};
use std::env;
use std::error::Error;
use std::fs::{self, DirEntry, File};
use std::io::Write;
use std::path::Path;
use tracing::{error, info};
use url;

// const USER_AGENT: &str = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36";

#[derive(Debug)]
enum WaterMarkSize {
    MD,
    LG,
}

pub async fn download_ts(
    context: &Context,
    video_id: &str,
    m3u8_url: &str,
    referer: Option<&str>,
) -> Result<String, Box<dyn Error>> {
    // let m3u8_url = m3u8_url.unwrap();
    let last_slash_index = m3u8_url.rfind('/').ok_or("invalid m3u8")?;
    info!("Last index of '/': {:?}", last_slash_index);
    let dir_path = &m3u8_url[..=last_slash_index];
    let mut file_name = &m3u8_url[last_slash_index + 1..];
    if let Some(first_part) = file_name.split("?").next() {
        file_name = first_part;
    }
    // println!("dir_path: {}", dir_path);

    let content = http::fetch(
        context,
        m3u8_url,
        referer,
        context.config.http_config.timeout_short,
    )
    .await?;
    let bytes = content.to_vec();
    let byte_slice: &[u8] = bytes.as_slice();
    let m3u8_parsed = m3u8_rs::parse_playlist_res(byte_slice);

    let result_filename = match m3u8_parsed {
        Ok(Playlist::MasterPlaylist(pl)) => {
            println!("Master playlist:\n{:?}", pl);
            if !file_name.ends_with(".m3u8") {
                format!("{}.m3u8", file_name)
            } else {
                file_name.to_string()
            }
        }
        Ok(Playlist::MediaPlaylist(mut pl)) => {
            // println!("Media playlist:\n{:?}", pl);
            for segment in &mut pl.segments {
                if let Some(key) = &mut segment.key {
                    let key_file_name = key.uri.clone().unwrap();
                    info!("key uri:{}", key_file_name);
                    let data_file_path = format!("{}{}", dir_path, key_file_name.as_str());
                    // let data_file_path = "https://s2.playhls.com/inc/enc.key";
                    info!("data url:{}", data_file_path);
                    // if the key has sub folder, change the key.uri to have the sub folder removed
                    let mut key_file_name = key_file_name.as_str();
                    if let Some(last_slash_index) = key_file_name.rfind('/') {
                        key_file_name = &key_file_name[last_slash_index + 1..];
                    }

                    key.uri = Some(key_file_name.to_string());
                    save_tmp_file_from_url(
                        context,
                        video_id,
                        &data_file_path,
                        referer,
                        false,
                        false,
                        key_file_name,
                        context.config.http_config.timeout_short,
                        false,
                    )
                    .await?;
                }

                let origin_uri = segment.uri.clone();

                // Get just the filename, handling both full URLs and paths
                let file_name = if origin_uri.starts_with("http") {
                    // For full URLs, parse and get last path segment
                    let url = url::Url::parse(&origin_uri)?;
                    let path = url.path().to_string();
                    path.split('/').last().unwrap_or(&origin_uri).to_string()
                } else {
                    // For paths, just get last segment
                    origin_uri
                        .split('/')
                        .last()
                        .unwrap_or(&origin_uri)
                        .to_string()
                };

                // Remove query string if present
                let file_name = file_name
                    .split('?')
                    .next()
                    .unwrap_or(&file_name)
                    .to_string();

                // Set segment URI to just the filename
                segment.uri = file_name.clone();

                // Build full download URL
                let download_url = if origin_uri.starts_with("http") {
                    origin_uri.to_string()
                } else {
                    format!("{}{}", dir_path, origin_uri)
                };

                save_tmp_file_from_url(
                    context,
                    video_id,
                    &download_url,
                    None,
                    false,
                    false,
                    file_name.as_str(),
                    context.config.http_config.timeout_short,
                    false,
                )
                .await?;
            }

            let mut serialized_bytes = Vec::new();
            pl.write_to(&mut serialized_bytes)?;

            let result_name = if !file_name.ends_with(".m3u8") {
                format!("{}.m3u8", file_name)
            } else {
                file_name.to_string()
            };
            save_tmp_file(video_id, &result_name, false, serialized_bytes.as_slice())?;
            result_name
        }
        Err(e) => {
            error!("parsining m3u8 error: {}", m3u8_url);
            return Err(Box::new(FetchVideoError(e.to_string())));
        }
    };
    Ok(result_filename)
}

pub async fn save_tmp_file_from_url(
    context: &Context,
    video_id: &str,
    url: &str,
    referer: Option<&str>,
    use_sub_folder: bool,
    overwrite: bool,
    file_name: &str,
    timeout: u64,
    with_curl: bool,
) -> Result<String, Box<dyn Error>> {
    let movie_cache_dir = env::var("MOVIE_CACHE_DIR")?;
    // Compose the file path
    let mut path = Path::new(movie_cache_dir.as_str()).join(video_id);
    if use_sub_folder {
        path = path.join("output");
    }
    path = path.join(file_name);

    info!("file_path :{:?}", path);

    // Check if the file exists and has a non-zero size
    if let Ok(metadata) = fs::metadata(&path) {
        if metadata.is_file() && metadata.len() > 0 && !overwrite {
            println!("File '{:?}' already exists and has a non-zero size.", &path);
            return Ok(path.to_string_lossy().into());
        }
    }
    // Create the directory if it doesn't exist
    fs::create_dir_all(path.parent().unwrap())?;

    // let file_path: String = path.to_string_lossy().into();
    let mut file = File::create(&path)?;

    info!("going to download:{}", url);

    let content = if with_curl {
        http::fetch_with_curl(context, url, referer, timeout).await?
    } else {
        http::fetch(context, url, referer, timeout).await?
    };
    file.write_all(&content)?;

    Ok(path.to_string_lossy().into())
}

pub fn save_tmp_file(
    video_id: &str,
    file_name: &str,
    use_sub_folder: bool,
    content: &[u8],
) -> Result<(), Box<dyn Error>> {
    let movie_cache_dir = env::var("MOVIE_CACHE_DIR")?;
    // let video_id = "abcd1234";
    // Compose the file path
    let mut path = Path::new(movie_cache_dir.as_str()).join(video_id);
    if use_sub_folder {
        path = path.join("output");
    }
    path = path.join(file_name);

    println!("file_path :{:?}", path);

    // Check if the file exists and has a non-zero size
    if let Ok(metadata) = fs::metadata(&path) {
        if metadata.is_file() && metadata.len() > 0 {
            println!("File '{:?}' already exists and has a non-zero size.", &path);
            return Ok(());
        }
    }
    // Create the directory if it doesn't exist
    fs::create_dir_all(path.parent().unwrap())?;

    // let file_path: String = path.to_string_lossy().into();
    let mut file = File::create(&path)?;
    file.write_all(&content)?;
    Ok(())
}

pub fn copy_to_tmp_file(
    video_id: &str,
    source_file: &str,
    file_name: &str,
) -> Result<String, Box<dyn Error>> {
    let movie_cache_dir = env::var("MOVIE_CACHE_DIR")?;
    // let video_id = "abcd1234";
    // Compose the file path
    let target_path = Path::new(movie_cache_dir.as_str())
        .join(video_id)
        .join(file_name);
    let target_file = target_path.to_str().unwrap();

    println!("file_path :{:?}", target_file);

    // Check if the file exists and has a non-zero size
    if let Ok(metadata) = fs::metadata(&target_path) {
        if metadata.is_file() && metadata.len() > 0 {
            println!(
                "File '{:?}' already exists and has a non-zero size.",
                &target_path
            );
            return Ok(String::from(target_file));
        }
    }

    // Create the directory if it doesn't exist
    fs::create_dir_all(target_path.parent().unwrap())?;

    fs::copy(source_file, target_file)?;
    Ok(String::from(target_file))
}

pub fn combine_ts(
    video_id: &str,
    file_name: &str,
    out_file_name: &str,
) -> Result<String, Box<dyn Error>> {
    let movie_cache_dir = env::var("MOVIE_CACHE_DIR")?;
    // let file_name = "playlist.m3u8";
    let playlist_path = Path::new(movie_cache_dir.as_str())
        .join(video_id)
        .join(file_name);
    let file_path = Path::new(movie_cache_dir.as_str())
        .join(video_id)
        .join("output")
        .join(out_file_name);
    // Create the directory if it doesn't exist
    fs::create_dir_all(file_path.parent().unwrap())?;

    let playlist_file = playlist_path.to_str().ok_or("can not form playlist path")?;
    let out_file = file_path.to_str().ok_or("can not form out file path")?;
    ffmpeg::combine_ts(playlist_file, out_file)?;

    Ok(String::from(out_file))
}

pub fn resize_video(
    file_path: &str,
    out_file_name: &str,
    rotate: i8,
) -> Result<(String, usize), Box<dyn Error>> {
    let resolution = ffmpeg::get_resolution(file_path)?;
    println!("width: {}, height: {}", resolution.0, resolution.1);

    let mut h = 720;
    let mut wm_size = WaterMarkSize::MD;
    // if landscape, max h = 720
    if resolution.0 > resolution.1 {
        if resolution.1 <= 720 {
            h = resolution.1;
        } else {
            wm_size = WaterMarkSize::LG;
        }
    } else {
        // if portrait, max h = 1080
        if resolution.1 > 1280 {
            h = 1280;
            wm_size = WaterMarkSize::LG;
        } else {
            h = resolution.1;
        }
    }

    println!("wm size:{:?}", wm_size);
    println!("going to resize video to h:{}", h);
    let resized_file = _resize_video(file_path, h, out_file_name, rotate, wm_size)?;
    Ok((resized_file, h))
}

fn _resize_video(
    input_file: &str,
    height: usize,
    out_file_name: &str,
    rotate: i8,
    wm_size: WaterMarkSize,
) -> Result<String, Box<dyn Error>> {
    let output_path = Path::new(input_file).parent().unwrap().join(out_file_name);
    let output_file = output_path.to_str().ok_or("can not form output file")?;
    if let Ok(metadata) = fs::metadata(output_file) {
        if metadata.is_file() && metadata.len() > 0 {
            println!(
                "File '{:?}' already exists and has a non-zero size.",
                output_file
            );
            return Ok(String::from(output_file));
        }
    }

    let wm_file = match wm_size {
        WaterMarkSize::MD => "rsc/watermark_md.png",
        WaterMarkSize::LG => "rsc/watermark_lg.png",
    };
    // -1 = do nothing
    //1 = 90° clockwise
    //2 = 90° counterclockwise
    ffmpeg::resize_and_add_watermark(input_file, wm_file, height, output_file, rotate)?;

    Ok(String::from(output_file))
}

pub fn split_ts(input_file: &str, out_dir_name: &str) -> Result<(), Box<dyn Error>> {
    let output_path = Path::new(input_file).parent().unwrap().join(out_dir_name);
    let output_dir = output_path.to_str().ok_or("can not form output dir")?;
    ffmpeg::split_to_ts(input_file, output_dir, "5")?;
    Ok(())
}

pub fn gen_thumbnail(
    input_file: &str,
    out_dir_name: &str,
    duration: f64,
) -> Result<(), Box<dyn Error>> {
    // fn gen_thumbnail(thumb_output_dir: &str, duration: f64) -> Result<(), Box<dyn Error>> {
    let interval = 8;
    let thumb_dir = gen_thumb_images(input_file, out_dir_name, interval)?;

    println!("thumb dir:{}", thumb_dir.as_str());
    let files = read_dir_and_sort(thumb_dir.as_str())?;

    let mut thumbs = Vec::new();
    let mut thumb_files = Vec::new();

    for file in files {
        // let file = file?;
        let file_name = file.file_name().into_string().unwrap();

        if file_name.starts_with("tmp") {
            let file_path = file.path();
            let image = image::open(file_path)?;

            thumbs.push(image);
            thumb_files.push(file_name);
        }
    }

    if thumbs.len() == 0 {
        return Ok(());
    }

    let img_width = thumbs[0].width();
    let img_height = thumbs[0].height();

    let columns = 4;
    let img_rows = (thumbs.len() as f64 / columns as f64).ceil() as u32;

    println!("single image dimension: {}x{}", img_width, img_height);
    println!(
        "new image dimension: {}x{}",
        img_width * columns,
        img_height * img_rows
    );

    let padding_columns = 0;
    let single_padd = 0;
    let padding_rows = 0;

    let mut dst = RgbaImage::new(
        img_width * columns + padding_columns,
        img_height * img_rows + padding_rows,
    );

    let mut x = 0;
    let mut cur_row = 0;
    let header_height = 0;

    let mut timestamps = Vec::new();
    for i in 0..=(duration as usize / 8) {
        let timestamp = Utc
            .timestamp_opt(i as i64 * 8, 0)
            .single()
            .ok_or("can not convert time")?
            .format("%H:%M:%S")
            .to_string();

        timestamps.push(timestamp);
    }
    timestamps.push(
        Utc.timestamp_opt(duration as i64, 0)
            .single()
            .ok_or("can not convert time")?
            .format("%H:%M:%S")
            .to_string(),
    );

    let img_name = "thumbnail.jpg";
    let mut vtt_content = String::from("WEBVTT\n");

    for (idx, thumb) in thumbs.iter().enumerate() {
        if x >= columns {
            x = 0;
            cur_row += 1;
        }

        let x_pos = (x * img_width) + single_padd;
        let y_pos = (cur_row * img_height) + single_padd;

        let dp = imageops::overlay(&mut dst, thumb, x_pos as i64, y_pos as i64);
        x += 1;

        vtt_content.push_str(&format!(
            "\n{}.000 --> {}.000\n{}#xywh={},{},{},{}\n",
            timestamps[idx],
            timestamps[idx + 1],
            img_name,
            x_pos,
            y_pos + header_height,
            img_width,
            img_height,
        ));
    }

    let thumb_output_path = Path::new(thumb_dir.as_str());

    let img_path = thumb_output_path.join(img_name);
    let mut img_file = fs::File::create(&img_path)?;
    // Encode the image to JPEG
    // Convert the RGBAImage to a DynamicImage
    let dynamic_image = DynamicImage::ImageRgba8(dst);
    let dynamic_image = dynamic_image.to_rgb8();

    // Encode the image to JPEG
    let encoder = JpegEncoder::new_with_quality(&mut img_file, 85);
    dynamic_image.write_with_encoder(encoder)?;

    let vtt_file_path = thumb_output_path.join("thumbnail.vtt");
    fs::write(&vtt_file_path, vtt_content)?;

    println!("Saved vtt to {:?}", vtt_file_path);

    // remove tmp thumb image files
    for thumb_file in &thumb_files {
        let thumb_file_path = thumb_output_path.join(thumb_file);
        fs::remove_file(thumb_file_path)?;
    }

    Ok(())
}

fn gen_thumb_images(
    input_file: &str,
    out_dir_name: &str,
    interval: usize,
) -> Result<String, Box<dyn Error>> {
    let output_path = Path::new(input_file)
        .parent()
        .unwrap()
        .join("output_thumbs.mp4");
    let output_file = output_path.to_str().ok_or("can not form output file")?;
    let thumb_path = Path::new(input_file).parent().unwrap().join(out_dir_name);
    fs::create_dir_all(&thumb_path)?;
    let thumb_dir = thumb_path.to_str().ok_or("can not form thumb dir")?;
    println!("generating thumbnails to: {}", thumb_dir);

    if let Ok(metadata) = fs::metadata(output_file) {
        if metadata.is_file() && metadata.len() > 0 {
            println!(
                "File '{:?}' already exists and has a non-zero size.",
                output_file
            );
            return Ok(String::from(thumb_dir));
        }
    }
    ffmpeg::resize(input_file, 100, output_file)?;
    ffmpeg::gen_thumbnail(output_file, thumb_dir, interval)?;

    Ok(String::from(thumb_dir))
}

pub fn get_cover_image(
    input_file: &str,
    out_file_name: &str,
    duration: f64,
) -> Result<(), Box<dyn Error>> {
    let cover_image_path = Path::new(input_file).parent().unwrap().join(out_file_name);
    let cover_image_file = cover_image_path.to_str().unwrap();

    if let Ok(metadata) = fs::metadata(cover_image_file) {
        if metadata.is_file() && metadata.len() > 0 {
            println!(
                "File '{:?}' already exists and has a non-zero size.",
                cover_image_file
            );
            return Ok(());
        }
    }
    let resolution = ffmpeg::get_resolution(input_file)?;
    // this is portrait, need to take 2 screenshots and combine them
    if resolution.1 as f64 / resolution.0 as f64 > 1.5 {
        ffmpeg::screenshot2(input_file, duration, cover_image_file)?;
    } else {
        ffmpeg::screenshot(input_file, 2, cover_image_file)?;
    }
    Ok(())
}

pub async fn upload_s3(
    context: &Context,
    video_id: &str,
    folder: &str,
    height: usize,
    use_sub_folder: bool,
) -> Result<(), Box<dyn Error>> {
    // s3::list_buckets().await?;
    // return Ok(());
    let movie_cache_dir = env::var("MOVIE_CACHE_DIR")?;
    let mut video_path = Path::new(movie_cache_dir.as_str()).join(video_id);
    if use_sub_folder {
        video_path = video_path.join("output");
    }
    println!("folder to upload: {}", video_path.to_str().unwrap());

    upload_cover_image_s3(context, video_id, use_sub_folder, false).await?;

    let ts_path = video_path.join("ts");
    upload_folder(context, ts_path.to_str().unwrap(), video_id, folder).await?;

    let thumbs_path = video_path.join("thumbs");
    upload_folder(context, thumbs_path.to_str().unwrap(), video_id, "thumbs").await?;

    gen_index_m3u8(context, video_id, folder, height).await?;
    Ok(())
}

pub async fn upload_cover_image_s3(
    context: &Context,
    video_id: &str,
    use_sub_folder: bool,
    overwrite: bool,
) -> Result<(), Box<dyn Error>> {
    let movie_cache_dir = env::var("MOVIE_CACHE_DIR")?;
    let mut video_path = Path::new(movie_cache_dir.as_str()).join(video_id);
    if use_sub_folder {
        video_path = video_path.join("output");
    }
    let image_path = video_path.join("cover.jpg");
    println!("image path:{}", image_path.to_str().unwrap());
    let key = format!("hls/{}/cover.jpg", video_id);
    _upload_s3(
        context,
        image_path.to_str().unwrap(),
        key.as_str(),
        overwrite,
    )
    .await?;
    Ok(())
}

async fn upload_folder(
    context: &Context,
    folder_path: &str,
    video_id: &str,
    folder: &str,
) -> Result<(), Box<dyn Error>> {
    let entries = read_dir_and_sort(folder_path)?;

    for entry in entries {
        let path = entry.path();
        if path.is_file() {
            let key = format!(
                "hls/{}/{}/{}",
                video_id,
                folder,
                entry.file_name().to_str().unwrap()
            );
            // println!("file:{}", path.to_str().unwrap());
            // println!("key:{}", key);
            _upload_s3(context, path.to_str().unwrap(), key.as_str(), false).await?;
        }
    }
    Ok(())
}

pub async fn _upload_s3(
    context: &Context,
    file_path: &str,
    key: &str,
    overwrite: bool,
) -> Result<(), Box<dyn Error>> {
    let exist = context.s3_client.check_file_exist(key).await?;
    if exist && !overwrite {
        println!("{} already exist.", key);
        return Ok(());
    }
    let body = ByteStream::from_path(file_path).await?;

    println!("uploading:{}", key);
    context.s3_client.put_object(key, body).await?;
    Ok(())
}

async fn gen_index_m3u8(
    context: &Context,
    video_id: &str,
    folder: &str,
    height: usize,
) -> Result<(), Box<dyn Error>> {
    let mut bandwidth: u64 = 0;
    match height {
        v if v <= 360 => bandwidth = 640000,
        v if v > 360 && v <= 480 => bandwidth = 1280000,
        v if v > 480 && v <= 720 => bandwidth = 2560000,
        v if v > 720 && v <= 1080 => bandwidth = 5760000,
        v if v > 1080 => bandwidth = 11520000,
        _ => bandwidth = 23040000,
    }
    let stream = VariantStream {
        is_i_frame: false,
        uri: format!("{}/index.m3u8", folder),
        bandwidth: bandwidth,
        ..Default::default()
    };
    let playlist = MasterPlaylist {
        variants: vec![stream],
        ..Default::default()
    };

    let mut serialized_bytes = Vec::new();
    playlist.write_to(&mut serialized_bytes)?;

    let body = ByteStream::from(serialized_bytes);
    let key = format!("hls/{}/index.m3u8", video_id);
    context.s3_client.put_object(key.as_str(), body).await?;
    Ok(())
}

// Helper function to extract domain and video code from reference URL
fn extract_video_info(reference: &str) -> (String, String) {
    if let Ok(url) = url::Url::parse(reference) {
        // Get domain
        let domain = url.host_str().map(|h| h.to_string()).unwrap_or_default();

        // Get path segments and handle trailing slashes
        if let Some(segments) = url.path_segments() {
            let segments: Vec<&str> = segments.collect();
            // Find last non-empty segment
            if let Some(last) = segments.iter().rev().find(|&&s| !s.is_empty()) {
                let mut video_code = last.to_string();
                // Include query string if present
                if let Some(query) = url.query() {
                    video_code = format!("{}?{}", video_code, query);
                }
                return (domain, video_code);
            }
        }
    }
    (String::new(), String::from(reference))
}

pub async fn save_video_in_db(
    context: &Context,
    video_obj: Video,
) -> Result<crate::prisma::video::Data, Box<dyn Error>> {
    let current_time = Utc::now().with_timezone(&FixedOffset::east_opt(0).unwrap());

    // Extract domain and video code from the reference
    let (domain, video_code) = extract_video_info(&video_obj.reference);

    // Try to find existing video with similar reference from same domain
    let existing_video = context
        .db_client
        .video()
        .find_first(vec![
            // Using contains for video code and domain check
            video::r#ref::contains(domain.clone()),
            video::r#ref::contains(video_code.clone()),
        ])
        .exec()
        .await?;

    if let Some(video) = existing_video {
        // Double check if it's really the same video by comparing extracted info
        let (existing_domain, existing_code) = extract_video_info(&video.r#ref);
        if existing_code == video_code && existing_domain == domain {
            // Update existing video
            return Ok(context
                .db_client
                .video()
                .update(
                    video::id::equals(video.id.clone()),
                    vec![video::updated_at::set(Some(current_time))],
                )
                .exec()
                .await?);
        }
    }

    // Create new video if none exists
    let video_saved = context
        .db_client
        .video()
        .create(
            video_obj.name,
            video_obj.reference,
            video_obj.view_count,
            vec![
                video::description::set(video_obj.description),
                video::tags::set(video_obj.tags),
                video::vid::set(video_obj.vid),
            ],
        )
        .exec()
        .await?;

    Ok(video_saved)
}

pub async fn save_video_source_in_db(
    context: &Context,
    video_id: &str,
    resolution: i32,
) -> Result<crate::prisma::source::Data, Box<dyn Error>> {
    let folder = format!("{}-{}", video_id, resolution);
    let video_id = String::from(video_id);
    let source = context
        .db_client
        .source()
        .upsert(
            source::folder::equals(folder.clone()),
            source::create(video::id::equals(video_id), resolution, folder, vec![]),
            vec![],
        )
        .exec()
        .await
        .unwrap();
    Ok(source)
}

pub async fn publish_video(
    context: &Context,
    video_id: &str,
    duration: f64,
) -> Result<crate::prisma::video::Data, Box<dyn Error>> {
    let video_saved = context
        .db_client
        .video()
        .update(
            video::id::equals(String::from(video_id)),
            vec![video::published::set(true), video::duration::set(duration)],
        )
        .exec()
        .await
        .unwrap();

    Ok(video_saved)
}

fn read_dir_and_sort(dir: &str) -> Result<Vec<DirEntry>, Box<dyn Error>> {
    let entries = fs::read_dir(dir)?;
    let mut files: Vec<_> = entries.filter_map(|entry| entry.ok()).collect();

    // Sort the vector by file name
    files.sort_by(|a, b| a.file_name().cmp(&b.file_name()));
    Ok(files)
}
